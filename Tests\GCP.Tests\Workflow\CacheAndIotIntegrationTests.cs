using Xunit;
using Xunit.Abstractions;
using FluentAssertions;
using GCP.Tests.Infrastructure;
using GCP.FunctionPool.Flow.Services;
using GCP.FunctionPool.Flow.Models;
using GCP.DataAccess;
using GCP.Common;
using GCP.Iot.Models;
using GCP.Iot.Services;
using Microsoft.Extensions.DependencyInjection;
using EasyCaching.Core;
using Moq;
using GCP.Functions.Common;

namespace GCP.Tests.Workflow
{
    /// <summary>
    /// 缓存和设备动作集成测试类
    /// </summary>
    public class CacheAndIotIntegrationTests : DatabaseTestBase
    {
        private Mock<EquipmentCommunicationManager> _mockCommunicationManager;
        private Mock<EquipmentCommunicationTask> _mockEquipmentTask;

        public CacheAndIotIntegrationTests(ITestOutputHelper output) : base(output)
        {
            _mockCommunicationManager = new Mock<EquipmentCommunicationManager>();
            _mockEquipmentTask = new Mock<EquipmentCommunicationTask>();
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);
            services.AddScoped<DataCache>();
            services.AddScoped<DataIotEquipment>();
            
            // 配置内存缓存用于测试
            services.AddEasyCaching(options =>
            {
                options.UseInMemory("test-cache");
            });
            
            // 注册Mock服务
            services.AddSingleton(_mockCommunicationManager.Object);
        }

        [Fact]
        public async Task CacheDeviceData_ShouldStoreAndRetrieveSuccessfully()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            var dataIotEquipment = GetService<DataIotEquipment>();
            SetTestContext(dataCache);
            SetTestContext(dataIotEquipment);

            // 设置Mock行为 - 模拟设备读取
            var deviceValues = new Dictionary<string, object>
            {
                ["Temperature"] = 25.5,
                ["Humidity"] = 60.2,
                ["Status"] = "Running"
            };
            _mockEquipmentTask.Setup(x => x.GetCurrentValues()).Returns(deviceValues);
            _mockCommunicationManager.Setup(x => x.GetEquipmentTask("device001"))
                                   .Returns(_mockEquipmentTask.Object);

            // 1. 读取设备数据
            var readData = new DataIotEquipmentRead
            {
                Name = "读取设备数据",
                Description = "读取设备所有参数",
                EquipmentId = new DataValue { Type = "text", TextValue = "device001" }
            };

            var deviceData = await dataIotEquipment.ReadEquipmentAllVariables(readData);

            // 2. 将设备数据缓存
            var cacheData = new DataCacheWrite
            {
                Name = "缓存设备数据",
                Description = "将设备数据存储到缓存",
                CacheKey = new DataValue { Type = "text", TextValue = "device001_data" },
                CacheValue = new DataValue { Type = "script", ScriptValue = JsonHelper.Serialize(deviceData) },
                ExpirationSeconds = new DataValue { Type = "text", TextValue = "600" }
            };

            await dataCache.WriteCache(cacheData);

            // 3. 从缓存读取设备数据
            var readCacheData = new DataCacheRead
            {
                Name = "读取缓存的设备数据",
                Description = "从缓存获取设备数据",
                CacheKey = new DataValue { Type = "text", TextValue = "device001_data" },
                DefaultValue = new DataValue { Type = "text", TextValue = "{}" }
            };

            var cachedData = await dataCache.ReadCache(readCacheData);

            // Assert
            cachedData.Should().NotBeNull("缓存数据不应为空");
            var deserializedData = JsonHelper.Deserialize<Dictionary<string, object>>(cachedData.ToString());
            deserializedData["Temperature"].Should().Be(25.5);
            deserializedData["Humidity"].Should().Be(60.2);
            deserializedData["Status"].Should().Be("Running");

            Output.WriteLine($"设备数据缓存集成测试成功，缓存数据: {cachedData}");
        }

        [Fact]
        public async Task CacheDeviceConfiguration_ShouldOptimizePerformance()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            var dataIotEquipment = GetService<DataIotEquipment>();
            SetTestContext(dataCache);
            SetTestContext(dataIotEquipment);

            // 模拟设备配置数据
            var deviceConfig = new
            {
                DeviceId = "device001",
                SamplingInterval = 1000,
                Alarms = new[]
                {
                    new { Name = "HighTemperature", Threshold = 80.0 },
                    new { Name = "LowHumidity", Threshold = 30.0 }
                }
            };

            // 1. 缓存设备配置
            var cacheConfigData = new DataCacheWrite
            {
                Name = "缓存设备配置",
                Description = "缓存设备配置信息",
                CacheKey = new DataValue { Type = "text", TextValue = "device001_config" },
                CacheValue = new DataValue { Type = "script", ScriptValue = JsonHelper.Serialize(deviceConfig) },
                ExpirationSeconds = new DataValue { Type = "text", TextValue = "3600" } // 1小时
            };

            await dataCache.WriteCache(cacheConfigData);

            // 2. 检查配置是否存在
            var checkConfigExists = new DataCacheExists
            {
                Name = "检查配置缓存",
                Description = "检查设备配置是否已缓存",
                CacheKey = new DataValue { Type = "text", TextValue = "device001_config" }
            };

            var configExists = await dataCache.ExistsCache(checkConfigExists);

            // 3. 读取缓存的配置
            var readConfigData = new DataCacheRead
            {
                Name = "读取设备配置",
                Description = "从缓存读取设备配置",
                CacheKey = new DataValue { Type = "text", TextValue = "device001_config" },
                DefaultValue = new DataValue { Type = "text", TextValue = "{}" }
            };

            var cachedConfig = await dataCache.ReadCache(readConfigData);

            // Assert
            configExists.Should().BeTrue("设备配置应该已缓存");
            cachedConfig.Should().NotBeNull("缓存的配置不应为空");
            
            var deserializedConfig = JsonHelper.Deserialize<dynamic>(cachedConfig.ToString());
            deserializedConfig.DeviceId.Should().Be("device001");
            deserializedConfig.SamplingInterval.Should().Be(1000);

            Output.WriteLine($"设备配置缓存测试成功，配置存在: {configExists}");
        }

        [Fact]
        public async Task ProjectIsolation_ShouldWorkWithBothCacheAndIot()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache1 = GetService<DataCache>();
            var dataCache2 = GetService<DataCache>();
            var dataIotEquipment = GetService<DataIotEquipment>();
            
            // 设置不同项目的上下文
            SetTestContext(dataCache1, "project-001");
            SetTestContext(dataCache2, "project-002");
            SetTestContext(dataIotEquipment);

            // 设置Mock行为
            var deviceValues = new Dictionary<string, object>
            {
                ["ProjectSpecificData"] = "Project001Data"
            };
            _mockEquipmentTask.Setup(x => x.GetCurrentValues()).Returns(deviceValues);
            _mockCommunicationManager.Setup(x => x.GetEquipmentTask("shared_device"))
                                   .Returns(_mockEquipmentTask.Object);

            // 1. 项目1读取设备数据并缓存
            var readData = new DataIotEquipmentRead
            {
                Name = "项目1读取设备",
                Description = "项目1读取共享设备数据",
                EquipmentId = new DataValue { Type = "text", TextValue = "shared_device" }
            };

            var deviceData = await dataIotEquipment.ReadEquipmentAllVariables(readData);

            var cacheData1 = new DataCacheWrite
            {
                Name = "项目1缓存数据",
                Description = "项目1缓存设备数据",
                CacheKey = new DataValue { Type = "text", TextValue = "shared_device_data" },
                CacheValue = new DataValue { Type = "script", ScriptValue = JsonHelper.Serialize(deviceData) }
            };

            await dataCache1.WriteCache(cacheData1);

            // 2. 项目2尝试读取项目1的缓存数据
            var readCacheData2 = new DataCacheRead
            {
                Name = "项目2读取缓存",
                Description = "项目2尝试读取项目1的缓存",
                CacheKey = new DataValue { Type = "text", TextValue = "shared_device_data" },
                DefaultValue = new DataValue { Type = "text", TextValue = "not_found" }
            };

            var result = await dataCache2.ReadCache(readCacheData2);

            // 3. 项目1读取自己的缓存数据
            var readCacheData1 = new DataCacheRead
            {
                Name = "项目1读取缓存",
                Description = "项目1读取自己的缓存",
                CacheKey = new DataValue { Type = "text", TextValue = "shared_device_data" },
                DefaultValue = new DataValue { Type = "text", TextValue = "not_found" }
            };

            var project1Result = await dataCache1.ReadCache(readCacheData1);

            // Assert
            result.Should().Be("not_found", "项目2不应该能读取到项目1的缓存数据");
            project1Result.Should().NotBe("not_found", "项目1应该能读取到自己的缓存数据");

            Output.WriteLine($"项目隔离测试成功 - 项目1结果: {project1Result}, 项目2结果: {result}");
        }

        /// <summary>
        /// 设置缓存服务的测试上下文
        /// </summary>
        private void SetTestContext(DataCache service, string projectId = "test-project-001")
        {
            var context = new FunctionContext
            {
                globalData = new Dictionary<string, object>
                {
                    ["ProjectId"] = projectId
                },
                Args = new Dictionary<string, object>(),
                Persistence = false,
                trackId = TUID.NewTUID().ToString()
            };

            var dbContext = GetService<IDbContext>();
            context.LocalDbContext.Value = dbContext;

            context.Current = new FunctionProvider
            {
                FunctionName = "测试缓存动作",
                Level = 0,
                SeqNo = 0,
                IsFlow = false
            };

            var contextProperty = typeof(DataCache).GetProperty("Context");
            contextProperty?.SetValue(service, context);

            var cacheProperty = typeof(DataCache).GetProperty("Cache");
            cacheProperty?.SetValue(service, GetService<IEasyCachingProvider>());
        }

        /// <summary>
        /// 设置设备服务的测试上下文
        /// </summary>
        private void SetTestContext(DataIotEquipment service)
        {
            var context = new FunctionContext
            {
                globalData = new Dictionary<string, object>(),
                Args = new Dictionary<string, object>(),
                Persistence = false,
                trackId = TUID.NewTUID().ToString()
            };

            var dbContext = GetService<IDbContext>();
            context.LocalDbContext.Value = dbContext;

            context.Current = new FunctionProvider
            {
                FunctionName = "测试设备动作",
                Level = 0,
                SeqNo = 0,
                IsFlow = false
            };

            var contextProperty = typeof(DataIotEquipment).GetProperty("Context");
            contextProperty?.SetValue(service, context);
        }
    }
}
