﻿using GCP.Common;
using GCP.FunctionPool.Flow.Models;
using GCP.Functions.Common;
using System.Text;

namespace GCP.FunctionPool.Flow.Services
{
    class JobControl : DataBaseService
    {
        [Function("changeNextBeginTime", "修改下次开始时间")]
        public void ChangeNextBeginTime(JobChangeNextBeginTimeData data)
        {
            var engine = FlowUtils.GetEngine(Context.db);
            // 使用兼容的_data变量，确保脚本中的_data.xxx.xxx访问方式仍然有效
            var compatibleData = RootNodeHelper.PrepareScriptCompatibleData(Context.globalData);
            engine.SetValue("_data", compatibleData);
            engine.JavascriptFuncBeforeExec(Context);

            var time = data.NextBeginTime.GetDataValue<DateTime?>(engine, globalData: Context.globalData);
            if (time == null)
            {
                throw new CustomException("下次开始时间 不能为空");
            }

            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine(time.ToString());
            }

            Context.globalData["_NEXT_BEGIN_TIME"] = time;
        }
    }
}
