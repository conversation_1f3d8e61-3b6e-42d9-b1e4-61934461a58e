<template>
  <div class="enhanced-function-list">
    <div class="function-list-container">
      <!-- 左侧：函数分类树 -->
      <div class="function-tree-panel">
        <div class="panel-header">
          <h4>函数分类</h4>
          <t-input v-model="searchKeyword" placeholder="搜索函数..." size="small" clearable>
            <template #prefix-icon>
              <search-icon />
            </template>
          </t-input>
        </div>
        <div class="tree-container">
          <t-tree
            :data="filteredData"
            expand-all
            activable
            transition
            expand-on-click-node
            hover
            @click="onFunctionClick"
            @dblclick="onFunctionDblClick"
          >
            <template #label="{ node }">
              <div
                class="tree-node-label"
                @click="onNodeLabelClick(node.data || node)"
                @dblclick="onNodeLabelDblClick(node.data || node, $event)"
              >
                <span v-html="highlightSearchKeyword(node.label)"></span>
                <t-tag v-if="(node.data || node).script" size="small" theme="primary" variant="outline">
                  {{ getFunctionCategory((node.data || node).value) }}
                </t-tag>
              </div>
            </template>
          </t-tree>
        </div>
      </div>

      <!-- 右侧：函数详情 -->
      <div class="function-detail-panel">
        <div v-if="activeFunction" class="function-detail">
          <!-- 函数头部信息 -->
          <div class="function-header">
            <div class="function-title-section">
              <h2 class="function-title">{{ activeFunction.label }}</h2>
              <t-space>
                <t-tag theme="primary" size="medium">{{ getFunctionCategory(activeFunction.value) }}</t-tag>
                <t-button size="small" variant="outline" @click="copyToClipboard(activeFunction.script)">
                  <template #icon>
                    <copy-icon />
                  </template>
                  复制代码
                </t-button>
                <t-button
                  size="small"
                  theme="primary"
                  :disabled="!activeFunction"
                  @click="selectFunction(activeFunction)"
                >
                  <template #icon>
                    <check-icon />
                  </template>
                  选择此函数
                </t-button>
              </t-space>
            </div>
          </div>

          <!-- 函数使用方法 -->
          <div class="function-section">
            <h3>使用方法</h3>
            <div class="code-preview-container">
              <code-preview :code="activeFunction.script" lang="javascript" />
            </div>
          </div>

          <!-- 函数说明 -->
          <div class="function-section">
            <h3>功能说明</h3>
            <div class="function-description">
              <p>{{ activeFunction.remark }}</p>
            </div>
          </div>

          <!-- 参数说明 -->
          <div v-if="functionParameters.length > 0" class="function-section">
            <h3>参数说明</h3>
            <t-table :data="functionParameters" :columns="paramColumns" size="small" bordered />
          </div>

          <!-- 使用示例 -->
          <div class="function-section">
            <h3>使用示例</h3>
            <div class="examples-container">
              <div v-for="(example, index) in functionExamples" :key="index" class="example-item">
                <div class="example-header">
                  <h4>{{ example.title }}</h4>
                </div>
                <div class="example-code">
                  <code-preview :code="example.code" lang="javascript" />
                </div>
                <div v-if="example.result" class="example-result">
                  <span class="result-label">预期结果:</span>
                  <code class="result-code">{{ example.result }}</code>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 未选择函数时的提示 -->
        <div v-else class="no-selection">
          <div class="no-selection-content">
            <t-icon name="function" size="64px" style="color: var(--td-text-color-placeholder)" />
            <h3>选择一个函数查看详细说明</h3>
            <p>从左侧列表中选择函数，查看使用方法、参数说明和示例代码</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'EnhancedFunctionList',
};
</script>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { SearchIcon, CopyIcon, CheckIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import CodePreview from '@/components/code-preview/index.vue';
import { VALUE_TYPE_MAP } from './constants';
import { getFunctionCategories, type FunctionCategory } from '@/services/functionDataService';

// Props (暂时未使用)
// const props = defineProps<{
//   filterText?: string;
// }>();

// Emits
const emits = defineEmits<{
  dblclick: [func: any];
}>();

// 响应式数据
const searchKeyword = ref('');
const activeFunction = ref<any>(null);
const loading = ref(false);

// 函数数据
const functionData = ref<FunctionCategory[]>([]);

// 加载函数数据
const loadFunctionData = async () => {
  loading.value = true;
  try {
    const data = await getFunctionCategories();
    functionData.value = data || [];
    console.log(`EnhancedFunctionList: 已加载 ${data.length} 个函数分类`);
  } catch (error) {
    console.error('加载函数数据失败:', error);
    MessagePlugin.error('加载函数数据失败');
    functionData.value = [];
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadFunctionData();
});

// 计算属性
const filteredData = computed(() => {
  if (!searchKeyword.value) {
    return functionData.value;
  }

  const keyword = searchKeyword.value.toLowerCase();
  return functionData.value
    .map((category: any) => ({
      ...category,
      children: (category.children || []).filter(
        (func: any) => func.label?.toLowerCase().includes(keyword) || func.value?.toLowerCase().includes(keyword),
      ),
    }))
    .filter((category: any) => category.children && category.children.length > 0);
});

const functionParameters = computed(() => {
  if (!activeFunction.value?.script) return [];
  return getFunctionParameters(activeFunction.value.script);
});

const functionExamples = computed(() => {
  if (!activeFunction.value?.value) return [];
  return getFunctionExamples(activeFunction.value.value);
});

// 表格列定义
const paramColumns = [
  { colKey: 'name', title: '参数名', width: 120 },
  { colKey: 'type', title: '类型', width: 100 },
  { colKey: 'required', title: '必需', width: 80, cell: (_: any, { row }: any) => (row.required ? '是' : '否') },
  { colKey: 'description', title: '说明', ellipsis: true },
];

// 方法
const onFunctionClick = (context: any) => {
  // TDesign Tree 组件的数据结构
  let nodeData = null;

  // 检查是否有 node 属性（TDesign Tree 的标准结构）
  if (context && context.node) {
    // TDesign Tree 的节点数据通常在 node.data 中
    nodeData = context.node.data;
  } else if (context && context.data) {
    nodeData = context.data;
  } else if (context && typeof context === 'object' && context.value) {
    // 直接是节点数据
    nodeData = context;
  }

  // 检查是否是叶子节点且有script属性
  if (nodeData && nodeData.script && nodeData.value) {
    activeFunction.value = nodeData;
  }
};

const onFunctionDblClick = (context: any) => {
  // TDesign Tree 组件的数据结构
  let nodeData = null;

  // 检查是否有 node 属性（TDesign Tree 的标准结构）
  if (context && context.node) {
    // TDesign Tree 的节点数据通常在 node.data 中
    nodeData = context.node.data;
  } else if (context && context.data) {
    nodeData = context.data;
  } else if (context && typeof context === 'object' && context.value) {
    // 直接是节点数据
    nodeData = context;
  }

  // 检查是否是叶子节点且有script属性
  if (nodeData && nodeData.script && nodeData.value) {
    selectFunction(nodeData);
  }
};

const selectFunction = (func?: any) => {
  const selectedFunc = func || activeFunction.value;

  if (selectedFunc) {
    emits('dblclick', selectedFunc);
  }
};

// 节点标签点击事件（直接处理节点数据）
const onNodeLabelClick = (nodeData: any) => {
  if (nodeData && nodeData.script && nodeData.value) {
    activeFunction.value = nodeData;
  }
};

// 节点标签双击事件（直接处理节点数据）
const onNodeLabelDblClick = (nodeData: any, event?: Event) => {
  // 阻止事件冒泡
  if (event) {
    event.stopPropagation();
  }

  if (nodeData && nodeData.script && nodeData.value) {
    selectFunction(nodeData);
  }
};

// 获取函数分类（基于输出类型）
const getFunctionCategory = (functionValue: string) => {
  const outputTypeMap: Record<string, string> = {
    // 字符串输出
    UUID: 'string',
    STRING_CONCAT: 'string',
    STRING_SUBSTRING: 'string',
    STRING_REPLACE: 'string',
    STRING_TRIM: 'string',
    STRING_UPPER: 'string',
    STRING_LOWER: 'string',
    DATE_TO_STRING: 'string',
    DATE_FORMAT: 'string',
    ARRAY_JOIN: 'string',

    // 数字输出
    ARRAY_LENGTH: 'decimal',
    STRING_LENGTH: 'decimal',
    DATE_DIFF: 'decimal',
    DATE_TIMESTAMP: 'decimal',
    MATH_CALC: 'decimal',
    MATH_ROUND: 'decimal',
    MATH_CEIL: 'decimal',
    MATH_FLOOR: 'decimal',
    MATH_ABS: 'decimal',

    // 布尔值输出
    IF: 'bool',
    COMPARE: 'bool',
    ARRAY_CONTAINS: 'bool',
    STRING_CONTAINS: 'bool',
    STRING_STARTS_WITH: 'bool',
    STRING_ENDS_WITH: 'bool',
    DICT_HAS_KEY: 'bool',

    // 日期输出
    NOW: 'DateTime',
    DATE_ADD: 'DateTime',
    DATE_PARSE: 'DateTime',
    DATE_FROM_TIMESTAMP: 'DateTime',

    // 数组输出
    STRING_SPLIT: 'array',
    DICT_KEYS: 'array',
    DICT_VALUES: 'array',
    GROUP_BY_DYNAMIC_FIELD: 'array',

    // 对象输出
    JSON_PARSE: 'object',
    DICT_GET: 'object',
    DICT_SET: 'object',
    DICT_MERGE: 'object',
    DB_FIRST: 'object',

    // 任意类型输出
    ARRAY_FIRST: 'object',
    ARRAY_LAST: 'object',
  };

  const typeKey = outputTypeMap[functionValue] || 'object';
  return VALUE_TYPE_MAP[typeKey] || '字典';
};

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    MessagePlugin.success('代码已复制到剪贴板');
  } catch (err) {
    console.error('复制失败:', err);
    MessagePlugin.error('复制失败');
  }
};

// 解析函数参数
const getFunctionParameters = (script: string) => {
  const params: Array<{ name: string; type: string; required: boolean; description?: string }> = [];

  const match = script.match(/\(([^)]*)\)/);
  if (match && match[1]) {
    const paramStr = match[1];
    const paramNames = paramStr
      .split(',')
      .map((p) => p.trim())
      .filter((p) => p && !p.includes('"'));

    paramNames.forEach((name) => {
      const paramInfo = getParameterInfo(name);
      params.push({
        name: name,
        type: paramInfo.type,
        required: paramInfo.required,
        description: paramInfo.description,
      });
    });
  }

  return params;
};

// 获取参数信息
const getParameterInfo = (paramName: string) => {
  const paramInfoMap: Record<string, { type: string; required: boolean; description?: string }> = {
    input: { type: 'string', required: true, description: '输入字符串' },
    array: { type: 'array', required: true, description: '输入数组' },
    dict: { type: 'object', required: true, description: '输入字典对象' },
    date: { type: 'Date', required: true, description: '日期对象' },
    date1: { type: 'Date', required: true, description: '第一个日期' },
    date2: { type: 'Date', required: true, description: '第二个日期' },
    value: { type: 'any', required: true, description: '输入值' },
    value1: { type: 'any', required: true, description: '第一个值' },
    value2: { type: 'any', required: true, description: '第二个值' },
    condition: { type: 'boolean', required: true, description: '条件表达式' },
    trueValue: { type: 'any', required: true, description: '条件为真时的值' },
    falseValue: { type: 'any', required: true, description: '条件为假时的值' },
    key: { type: 'string', required: true, description: '键名' },
    time: { type: 'Date', required: true, description: '时间对象' },
    jsonString: { type: 'string', required: true, description: 'JSON字符串' },
    dateString: { type: 'string', required: true, description: '日期字符串' },
    timestamp: { type: 'number', required: true, description: '时间戳' },
    separator: { type: 'string', required: false, description: '分隔符，默认为逗号' },
    format: { type: 'string', required: false, description: '格式字符串' },
    startIndex: { type: 'number', required: true, description: '起始位置' },
    length: { type: 'number', required: false, description: '长度' },
    oldValue: { type: 'string', required: true, description: '要替换的值' },
    newValue: { type: 'string', required: true, description: '新值' },
    defaultValue: { type: 'any', required: false, description: '默认值' },
    digits: { type: 'number', required: false, description: '小数位数，默认为0' },
    unit: { type: 'string', required: false, description: '时间单位' },
    groupFields: { type: 'array', required: true, description: '分组字段数组' },
    fields: { type: 'array', required: true, description: '其他字段数组' },
    children: { type: 'string', required: false, description: '子节点字段名' },
    dict1: { type: 'object', required: true, description: '第一个字典' },
    dict2: { type: 'object', required: true, description: '第二个字典' },
    params: { type: 'object', required: false, description: '查询参数' },
    '...': { type: 'any', required: false, description: '可变参数' },
  };

  return paramInfoMap[paramName] || { type: 'any', required: true };
};

// 获取函数使用示例
const getFunctionExamples = (functionValue: string) => {
  const examplesMap: Record<string, Array<{ title: string; code: string; result?: string }>> = {
    // 字符串函数示例
    STRING_CONCAT: [
      { title: '基本用法', code: 'Utils.STRING_CONCAT("Hello", " ", "World")', result: '"Hello World"' },
      { title: '变量拼接', code: 'Utils.STRING_CONCAT(user.firstName, " ", user.lastName)' },
    ],
    STRING_UPPER: [{ title: '转换大写', code: 'Utils.STRING_UPPER("hello world")', result: '"HELLO WORLD"' }],
    STRING_LOWER: [{ title: '转换小写', code: 'Utils.STRING_LOWER("HELLO WORLD")', result: '"hello world"' }],
    STRING_SUBSTRING: [{ title: '截取字符串', code: 'Utils.STRING_SUBSTRING("Hello World", 0, 5)', result: '"Hello"' }],
    STRING_REPLACE: [
      { title: '替换内容', code: 'Utils.STRING_REPLACE("Hello World", "World", "Vue")', result: '"Hello Vue"' },
    ],
    STRING_TRIM: [{ title: '去除空格', code: 'Utils.STRING_TRIM("  Hello World  ")', result: '"Hello World"' }],
    STRING_SPLIT: [{ title: '分割字符串', code: 'Utils.STRING_SPLIT("a,b,c", ",")', result: '["a", "b", "c"]' }],
    STRING_LENGTH: [{ title: '获取长度', code: 'Utils.STRING_LENGTH("Hello")', result: '5' }],
    STRING_CONTAINS: [{ title: '包含检查', code: 'Utils.STRING_CONTAINS("Hello World", "World")', result: 'true' }],

    // 数组函数示例
    ARRAY_LENGTH: [{ title: '获取数组长度', code: 'Utils.ARRAY_LENGTH([1, 2, 3, 4])', result: '4' }],
    ARRAY_FIRST: [{ title: '获取第一个元素', code: 'Utils.ARRAY_FIRST([1, 2, 3])', result: '1' }],
    ARRAY_LAST: [{ title: '获取最后一个元素', code: 'Utils.ARRAY_LAST([1, 2, 3])', result: '3' }],
    ARRAY_JOIN: [
      { title: '用逗号连接', code: 'Utils.ARRAY_JOIN([1, 2, 3], ",")', result: '"1,2,3"' },
      { title: '用分号连接', code: 'Utils.ARRAY_JOIN(["a", "b", "c"], ";")', result: '"a;b;c"' },
    ],
    ARRAY_CONTAINS: [{ title: '包含检查', code: 'Utils.ARRAY_CONTAINS([1, 2, 3], 2)', result: 'true' }],

    // 时间函数示例
    UUID: [{ title: '生成UUID', code: 'Utils.UUID()', result: '"550e8400-e29b-41d4-a716-************"' }],
    NOW: [{ title: '获取当前时间', code: 'Utils.NOW()', result: '2024-01-15 14:30:25' }],
    DATE_TO_STRING: [
      { title: '格式化时间', code: 'Utils.DATE_TO_STRING(new Date(), "yyyy-MM-dd")', result: '"2024-01-15"' },
    ],
    DATE_ADD: [
      { title: '增加天数', code: 'Utils.DATE_ADD(new Date(), 7, "days")', result: '7天后的日期' },
      { title: '减少小时', code: 'Utils.DATE_ADD(new Date(), -2, "hours")', result: '2小时前的时间' },
    ],
    DATE_FORMAT: [
      {
        title: '标准格式',
        code: 'Utils.DATE_FORMAT(new Date(), "yyyy-MM-dd HH:mm:ss")',
        result: '"2024-01-15 14:30:25"',
      },
      { title: '简短格式', code: 'Utils.DATE_FORMAT(new Date(), "MM/dd/yyyy")', result: '"01/15/2024"' },
    ],
    DATE_DIFF: [{ title: '计算天数差', code: 'Utils.DATE_DIFF(date1, date2, "days")', result: '5' }],

    // 数学函数示例
    MATH_CALC: [
      { title: '加法运算', code: 'Utils.MATH_CALC(10, "+", 5)', result: '15' },
      { title: '乘法运算', code: 'Utils.MATH_CALC(3, "*", 4)', result: '12' },
    ],
    MATH_ROUND: [
      { title: '四舍五入到整数', code: 'Utils.MATH_ROUND(3.14159)', result: '3' },
      { title: '保留两位小数', code: 'Utils.MATH_ROUND(3.14159, 2)', result: '3.14' },
    ],
    MATH_CEIL: [{ title: '向上取整', code: 'Utils.MATH_CEIL(3.14)', result: '4' }],
    MATH_FLOOR: [{ title: '向下取整', code: 'Utils.MATH_FLOOR(3.14)', result: '3' }],
    MATH_ABS: [{ title: '绝对值', code: 'Utils.MATH_ABS(-5)', result: '5' }],

    // 字典函数示例
    DICT_GET: [{ title: '获取字典值', code: 'Utils.DICT_GET(user, "name", "Unknown")', result: '"John Doe"' }],
    DICT_SET: [{ title: '设置字典值', code: 'Utils.DICT_SET(user, "age", 25)', result: '{ name: "John", age: 25 }' }],
    DICT_HAS_KEY: [{ title: '检查键存在', code: 'Utils.DICT_HAS_KEY(user, "name")', result: 'true' }],

    // 逻辑函数示例
    IF: [{ title: '条件判断', code: 'Utils.IF(age >= 18, "成年", "未成年")', result: '"成年"' }],
    COMPARE: [{ title: '数值比较', code: 'Utils.COMPARE(score, ">=", 60)', result: 'true' }],

    // 其他函数示例
    JSON_PARSE: [{ title: '解析JSON', code: 'Utils.JSON_PARSE(\'{"name": "John"}\')', result: '{ name: "John" }' }],
    DB_FIRST: [{ title: '查询数据', code: 'Utils.DB_FIRST("SELECT * FROM users WHERE id = ?", [1])' }],
  };

  return examplesMap[functionValue] || [{ title: '基本用法', code: `Utils.${functionValue}(...)` }];
};

// 高亮搜索关键词
const highlightSearchKeyword = (text: string) => {
  if (!searchKeyword.value) {
    return text;
  }

  const keyword = searchKeyword.value.toLowerCase();
  const regex = new RegExp(`(${keyword})`, 'gi');
  return text.replace(
    regex,
    '<mark style="background: var(--td-warning-color-1); color: var(--td-warning-color-7); padding: 1px 2px; border-radius: 2px;">$1</mark>',
  );
};
</script>

<style lang="less" scoped>
.enhanced-function-list {
  height: 100%; /* 使用父容器的全部高度 */
  display: flex;
  flex-direction: column;

  .function-list-container {
    flex: 1;
    display: flex;
    min-height: 0;
    overflow: hidden;
  }

  .function-tree-panel {
    width: 320px;
    min-width: 280px;
    max-width: 400px;
    border-right: 1px solid var(--td-border-level-1-color);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;

    .panel-header {
      padding: 16px;
      border-bottom: 1px solid var(--td-border-level-1-color);
      background: var(--td-bg-color-container);

      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .tree-container {
      flex: 1;
      padding: 8px;
      overflow-y: auto;
    }
  }

  .function-detail-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    overflow: hidden;
    padding: 16px;
  }
}

.tree-node-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 4px 0;

  span {
    flex: 1;
    font-size: 13px;
    line-height: 1.4;
  }

  .t-tag {
    margin-left: 8px;
    flex-shrink: 0;
  }
}

/* 树节点样式优化 */
:deep(.t-tree__item) {
  margin-bottom: 2px;
}

:deep(.t-tree__item--leaf) {
  .t-tree__label {
    padding: 6px 8px;
    border-radius: 4px;
    transition: all 0.2s;
  }

  &:hover .t-tree__label {
    background: var(--td-bg-color-container-hover);
  }

  &.t-tree__item--active .t-tree__label {
    background: var(--td-brand-color-1);
    color: var(--td-brand-color);
  }
}

:deep(.t-tree__item--branch) {
  .t-tree__label {
    font-weight: 500;
    color: var(--td-text-color-primary);
  }
}

.function-detail {
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  .function-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--td-border-level-1-color);
    flex-shrink: 0;

    .function-title-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 16px;

      .function-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        flex: 1;
        min-width: 0;
        word-break: break-word;
      }

      .t-space {
        flex-shrink: 0;
      }
    }
  }

  .function-section {
    margin-bottom: 24px;
    flex-shrink: 0;

    h3 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 500;
      color: var(--td-text-color-primary);
    }

    .code-preview-container {
      border-radius: 6px;
      overflow: hidden;
    }

    .function-description {
      background: var(--td-bg-color-container-select);
      border-radius: 6px;
      line-height: 1.5;
    }
  }
}

.examples-container {
  .example-item {
    margin-bottom: 16px;
    padding: 16px;
    background: var(--td-bg-color-container);
    border: 1px solid var(--td-border-level-1-color);
    border-radius: 8px;

    .example-header {
      margin-bottom: 8px;

      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        color: var(--td-text-color-primary);
      }
    }

    .example-code {
      margin-bottom: 8px;
      border-radius: 4px;
      overflow: hidden;
    }

    .example-result {
      display: flex;
      align-items: center;
      gap: 8px;

      .result-label {
        font-size: 12px;
        color: var(--td-text-color-secondary);
      }

      .result-code {
        background: var(--td-success-color-1);
        color: var(--td-success-color-7);
        padding: 4px 8px;
        border-radius: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
      }
    }
  }
}

.no-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;

  .no-selection-content {
    text-align: center;
    color: var(--td-text-color-placeholder);
    max-width: 300px;

    .t-icon {
      margin-bottom: 16px;
      opacity: 0.6;
    }

    h3 {
      margin: 16px 0 8px 0;
      font-size: 18px;
      line-height: 1.4;
    }

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

/* Icon 样式优化 */
.t-button .t-icon {
  margin-right: 4px;
}

.t-button[size='small'] .t-icon {
  margin-right: 2px;
}
</style>
