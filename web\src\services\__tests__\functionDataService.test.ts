import { describe, it, expect, vi, beforeEach } from 'vitest';
import { functionDataService, getFunctionCategories, getAllFunctions } from '../functionDataService';
import { api, Services } from '@/api/system';

// Mock the API
vi.mock('@/api/system', () => ({
  api: {
    run: vi.fn()
  },
  Services: {
    utilsFunctionGetByCategory: '/gcp/utilsFunction/getByCategory'
  }
}));

// Mock the script completion module
vi.mock('@/components/editor/scriptCompletion', () => ({
  updateIntelliSenseData: vi.fn()
}));

describe('FunctionDataService', () => {
  const mockFunctionData = [
    {
      value: 'Common',
      label: '常用函数',
      children: [
        {
          value: 'UUID',
          label: '随机ID',
          script: 'Utils.UUID()',
          remark: '生成随机的UUID',
          returnType: 'string'
        },
        {
          value: 'NOW',
          label: '当前时间',
          script: 'Utils.NOW()',
          remark: '获取当前时间',
          returnType: 'DateTime'
        }
      ]
    },
    {
      value: 'String',
      label: '字符串函数',
      children: [
        {
          value: 'STRING_CONCAT',
          label: '字符串拼接',
          script: 'Utils.STRING_CONCAT(str1, str2)',
          remark: '拼接多个字符串',
          returnType: 'string'
        }
      ]
    }
  ];

  beforeEach(() => {
    // Clear cache before each test
    functionDataService.clearCache();
    vi.clearAllMocks();
  });

  it('should load function categories from API', async () => {
    // Arrange
    (api.run as any).mockResolvedValue(mockFunctionData);

    // Act
    const result = await getFunctionCategories();

    // Assert
    expect(api.run).toHaveBeenCalledWith(Services.utilsFunctionGetByCategory);
    expect(result).toEqual(mockFunctionData);
  });

  it('should extract all functions from categories', async () => {
    // Arrange
    (api.run as any).mockResolvedValue(mockFunctionData);

    // Act
    const result = await getAllFunctions();

    // Assert
    expect(result).toHaveLength(3); // 2 from Common + 1 from String
    expect(result[0]).toMatchObject({
      value: 'UUID',
      label: '随机ID',
      script: 'Utils.UUID()',
      remark: '生成随机的UUID',
      category: 'Common',
      categoryDisplayName: '常用函数'
    });
  });

  it('should cache function data and not call API twice', async () => {
    // Arrange
    (api.run as any).mockResolvedValue(mockFunctionData);

    // Act
    await getFunctionCategories();
    await getFunctionCategories(); // Second call should use cache

    // Assert
    expect(api.run).toHaveBeenCalledTimes(1);
  });

  it('should force refresh when requested', async () => {
    // Arrange
    (api.run as any).mockResolvedValue(mockFunctionData);

    // Act
    await getFunctionCategories();
    await getFunctionCategories(true); // Force refresh

    // Assert
    expect(api.run).toHaveBeenCalledTimes(2);
  });

  it('should handle API errors gracefully', async () => {
    // Arrange
    (api.run as any).mockRejectedValue(new Error('API Error'));

    // Act & Assert
    await expect(getFunctionCategories()).rejects.toThrow('加载函数数据失败: API Error');
  });

  it('should handle invalid API response', async () => {
    // Arrange
    (api.run as any).mockResolvedValue(null);

    // Act
    const result = await getFunctionCategories();

    // Assert
    expect(result).toEqual([]);
  });

  it('should search functions by keyword', async () => {
    // Arrange
    (api.run as any).mockResolvedValue(mockFunctionData);

    // Act
    const result = await functionDataService.searchFunctions('UUID');

    // Assert
    expect(result).toHaveLength(1);
    expect(result[0].value).toBe('UUID');
  });

  it('should get functions by category', async () => {
    // Arrange
    (api.run as any).mockResolvedValue(mockFunctionData);

    // Act
    const result = await functionDataService.getFunctionsByCategory('Common');

    // Assert
    expect(result).toHaveLength(2);
    expect(result.every(f => f.category === 'Common')).toBe(true);
  });

  it('should clear cache properly', async () => {
    // Arrange
    (api.run as any).mockResolvedValue(mockFunctionData);
    await getFunctionCategories();

    // Act
    functionDataService.clearCache();
    await getFunctionCategories();

    // Assert
    expect(api.run).toHaveBeenCalledTimes(2);
  });
});
