using GCP.Functions.Common.Services;
using Microsoft.Extensions.DependencyInjection;
using Xunit;
using Xunit.Abstractions;
using GCP.Tests.Infrastructure;
using FluentAssertions;

namespace GCP.Tests.Functions
{
    /// <summary>
    /// Utils工具函数服务测试
    /// </summary>
    public class UtilsFunctionServiceTests : TestBase
    {
        public UtilsFunctionServiceTests(ITestOutputHelper output) : base(output)
        {
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);

            // 添加UtilsFunctionService
            services.AddScoped<UtilsFunctionService>();
        }

        [Fact]
        public void GetAll_ShouldReturnFunctionList()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetAll();

            // Assert
            result.Should().NotBeNull();
            result.Should().NotBeEmpty();

            // 验证返回的函数包含基本字段
            var firstFunction = result.First() as dynamic;
            ((object)firstFunction).Should().NotBeNull();
            ((string)firstFunction.value).Should().NotBeNullOrEmpty();
            ((string)firstFunction.label).Should().NotBeNullOrEmpty();
            ((string)firstFunction.script).Should().NotBeNullOrEmpty();
            ((string)firstFunction.remark).Should().NotBeNullOrEmpty();
            ((string)firstFunction.category).Should().NotBeNullOrEmpty();
            
            Output.WriteLine($"获取到 {result.Count} 个工具函数");
            foreach (dynamic func in result.Take(5))
            {
                Output.WriteLine($"函数: {func.value} - {func.label} ({func.category})");
            }
        }

        [Fact]
        public void GetByCategory_ShouldReturnCategorizedFunctions()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetByCategory();

            // Assert
            result.Should().NotBeNull();
            result.Should().NotBeEmpty();

            // 验证分类结构
            var firstCategory = result.First() as dynamic;
            ((object)firstCategory).Should().NotBeNull();
            ((string)firstCategory.value).Should().NotBeNullOrEmpty();
            ((string)firstCategory.label).Should().NotBeNullOrEmpty();
            ((object)firstCategory.children).Should().NotBeNull();

            var children = firstCategory.children as List<object>;
            children.Should().NotBeNull();
            children.Should().NotBeEmpty();
            
            Output.WriteLine($"获取到 {result.Count} 个函数分类");
            foreach (dynamic category in result)
            {
                var categoryChildren = category.children as List<object>;
                Output.WriteLine($"分类: {category.label} - {categoryChildren?.Count ?? 0} 个函数");
            }
        }

        [Fact]
        public void GetAll_ShouldIncludeCommonFunctions()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetAll();

            // Assert
            var functionValues = result.Select(f => ((dynamic)f).value as string).ToList();
            
            // 验证包含常用函数
            functionValues.Should().Contain("UUID");
            functionValues.Should().Contain("NOW");
            functionValues.Should().Contain("STRING_CONCAT");
            functionValues.Should().Contain("ARRAY_LENGTH");
            functionValues.Should().Contain("DATE_ADD");
            functionValues.Should().Contain("DICT_GET");
            functionValues.Should().Contain("MATH_CALC");
            
            Output.WriteLine("验证常用函数存在: 通过");
        }

        [Fact]
        public void GetByCategory_ShouldHaveExpectedCategories()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetByCategory();

            // Assert
            var categoryValues = result.Select(c => ((dynamic)c).value as string).ToList();
            
            // 验证包含预期的分类
            categoryValues.Should().Contain("Common");
            categoryValues.Should().Contain("String");
            categoryValues.Should().Contain("Array");
            categoryValues.Should().Contain("DateTime");
            categoryValues.Should().Contain("Dictionary");
            categoryValues.Should().Contain("Math");
            
            Output.WriteLine("验证函数分类存在: 通过");
        }

        [Fact]
        public void Functions_ShouldHaveEnglishLabels()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetAll();

            // Assert
            foreach (dynamic func in result)
            {
                string label = func.label;
                string value = func.value;
                
                // 验证标签是英文的（不包含中文字符）
                bool hasChineseChars = label.Any(c => c >= 0x4e00 && c <= 0x9fff);
                hasChineseChars.Should().BeFalse($"函数 {value} 的标签 '{label}' 包含中文字符");
            }
            
            Output.WriteLine("验证函数标签为英文: 通过");
        }

        [Fact]
        public void ShowAllFunctions_ForDebugging()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetAll();

            // Assert & Output
            Output.WriteLine($"=== 所有Utils工具函数 (共{result.Count}个) ===");
            foreach (dynamic func in result)
            {
                Output.WriteLine($"函数: {func.value}");
                Output.WriteLine($"  显示名: {func.label}");
                Output.WriteLine($"  分类: {func.category}");
                Output.WriteLine($"  脚本: {func.script}");
                Output.WriteLine($"  描述: {func.remark}");
                Output.WriteLine($"  返回类型: {func.returnType}");
                Output.WriteLine("  ---");
            }
        }

        [Fact]
        public void ShowCategorizedFunctions_ForDebugging()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetByCategory();

            // Assert & Output
            Output.WriteLine($"=== 按分类的Utils工具函数 (共{result.Count}个分类) ===");
            foreach (dynamic category in result)
            {
                var children = category.children as List<object>;
                Output.WriteLine($"分类: {category.label} ({category.value}) - {children?.Count ?? 0} 个函数");

                if (children != null)
                {
                    foreach (dynamic func in children)
                    {
                        Output.WriteLine($"  - {func.label} ({func.value}): {func.script}");
                    }
                }
                Output.WriteLine("");
            }
        }

        [Fact]
        public void ShowParameterDescriptions_ForDebugging()
        {
            // Arrange
            var service = ServiceProvider.GetRequiredService<UtilsFunctionService>();

            // Act
            var result = service.GetAll();

            // Assert & Output
            Output.WriteLine($"=== 函数参数描述测试 ===");

            // 找几个有参数描述的函数进行测试
            var functionsWithParams = result.Where(f =>
            {
                var parameters = ((dynamic)f).parameters as List<object>;
                return parameters != null && parameters.Count > 0;
            }).Take(5);

            foreach (dynamic func in functionsWithParams)
            {
                Output.WriteLine($"函数: {func.label} ({func.value})");
                Output.WriteLine($"  输出类型: {func.outputType}");
                Output.WriteLine($"  描述: {func.remark}");

                var parameters = func.parameters as List<object>;
                if (parameters != null)
                {
                    Output.WriteLine("  参数:");
                    foreach (dynamic param in parameters)
                    {
                        Output.WriteLine($"    - {param.name} ({param.type}): {param.description}");
                    }
                }
                Output.WriteLine("  ---");
            }
        }
    }
}
