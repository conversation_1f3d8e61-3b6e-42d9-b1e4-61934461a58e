using System.Collections.Concurrent;
using GCP.Common;
using GCP.Eventbus.Infrastructure;
using Serilog;
using TouchSocket.Core;
using TouchSocket.Sockets;

namespace GCP.Eventbus.Providers
{
    class TcpMessageBus : MessageBusBase
    {
        private readonly ConcurrentDictionary<string, ConcurrentBag<TcpMessageConsumer>> _consumers;
        private readonly TcpService _tcpService;
        private readonly ConcurrentDictionary<string, TcpSessionClient> _clients; // 客户端管理
        private readonly TcpEventConfig _eventConfig; // 事件配置（在构造时初始化）
        private bool _isConnected;

        public override bool IsConnected => _isConnected;

        /// <summary>
        /// 是否支持自定义消息解析
        /// </summary>
        public bool SupportsCustomParsing => _eventConfig != null;

        /// <summary>
        /// 获取所有连接的客户端ID
        /// </summary>
        public List<string> GetClientIds()
        {
            return _tcpService.GetIds().ToList();
        }

        public TcpMessageBus(MessageBusOptions options)
            : base(options)
        {
            _consumers = new ConcurrentDictionary<string, ConcurrentBag<TcpMessageConsumer>>();
            _clients = new ConcurrentDictionary<string, TcpSessionClient>();
            _tcpService = new TcpService();
            _isConnected = false;

            // 在构造时初始化事件配置
            _eventConfig = InitializeEventConfig(options);

            // 配置TCP服务
            var config = new TouchSocketConfig()
                .SetListenIPHosts(Convert.ToInt32(options.Settings.GetValueOrDefault("Port", "7789")));

            _tcpService.SetupAsync(config);
            _tcpService.Received += OnTcpReceived;
            _tcpService.Connected += OnClientConnected;
            _tcpService.Closed += OnClientDisconnected;
        }

        private Task OnClientConnected(TcpSessionClient client, TouchSocketEventArgs e)
        {
            _clients[client.Id] = client;
            return Task.CompletedTask;
        }

        private Task OnClientDisconnected(TcpSessionClient client, TouchSocketEventArgs e)
        {
            _clients.TryRemove(client.Id, out _);
            return Task.CompletedTask;
        }

        private Task OnTcpReceived(TcpSessionClient client, ReceivedDataEventArgs e)
        {
            try
            {
                var message = e.ByteBlock.ToString();

                MessageEnvelope envelope = null;
                if (SupportsCustomParsing)
                {
                    envelope = ParseCustomMessage(message);
                }
                else
                {
                    envelope = JsonHelper.Deserialize<MessageEnvelope>(message);
                }

                if (envelope == null)
                {
                    Log.Debug("无法解析TCP消息: {Message}", message);
                    return Task.CompletedTask;
                }

                var topic = envelope.Headers.TryGetValue("Topic", out var topicObj) ? topicObj?.ToString() : null;
                if (string.IsNullOrEmpty(topic))
                {
                    Log.Debug("消息缺少Topic信息: {Message}", message);
                    return Task.CompletedTask;
                }

                if (_consumers.TryGetValue(topic, out var consumers))
                {
                    // 并发地将消息派发给所有订阅者
                    var handlingTasks = consumers.Select(consumer => consumer.HandleMessageAsync(envelope, CancellationToken.None));

                    // 使用 ContinueWith 在所有任务完成后统一记录错误，避免阻塞接收线程
                    _ = Task.WhenAll(handlingTasks).ContinueWith(task =>
                    {
                        if (task.IsFaulted)
                        {
                            Log.Error(task.Exception, "One or more handlers failed while processing a message for topic {Topic}.", topic);
                        }
                    }, TaskContinuationOptions.OnlyOnFaulted);
                }
                else
                {
                    Log.Debug("没有找到Topic {Topic} 的消费者", topic);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理TCP消息时发生错误");
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// 在构造时初始化事件配置
        /// </summary>
        private TcpEventConfig InitializeEventConfig(MessageBusOptions options)
        {
            try
            {
                var port = Convert.ToInt32(options.Settings.GetValueOrDefault("Port", "7789"));
                var eventId = options.Settings.GetValueOrDefault("EventId", "");
                var eventName = options.Settings.GetValueOrDefault("EventName", "");
                var uniqueCodeField = options.Settings.GetValueOrDefault("UniqueCodeField", "");
                var contentField = options.Settings.GetValueOrDefault("ContentField", "");

                // 如果没有配置唯一编码字段，则返回null（表示不支持自定义解析）
                if (string.IsNullOrEmpty(uniqueCodeField))
                {
                    Log.Information("TCP事件 {EventName} 未配置唯一编码字段，将只支持标准MessageEnvelope格式", eventName);
                    return null;
                }

                Log.Information("TCP事件 {EventName} 配置: Port={Port}, UniqueCodeField={UniqueCodeField}, ContentField={ContentField}",
                    eventName, port, uniqueCodeField, contentField);

                return new TcpEventConfig
                {
                    EventId = eventId,
                    EventName = eventName,
                    Port = port,
                    UniqueCodeField = uniqueCodeField,
                    ContentField = contentField
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, "初始化TCP事件配置时发生错误");
                return null;
            }
        }

        /// <summary>
        /// 根据事件配置解析自定义消息格式
        /// </summary>
        private MessageEnvelope ParseCustomMessage(string message)
        {
            try
            {
                // 解析为通用的 JSON 对象
                var jsonData = JsonHelper.Deserialize<Dictionary<string, object>>(message);
                if (jsonData == null) return null;

                // 根据配置提取唯一编码和内容
                var uniqueCode = ExtractFieldValue(jsonData, _eventConfig.UniqueCodeField);
                var content = ExtractFieldValue(jsonData, _eventConfig.ContentField);

                if (string.IsNullOrEmpty(uniqueCode))
                {
                    Log.Debug("无法从消息中提取唯一编码，字段: {UniqueCodeField}, 消息: {Message}",
                        _eventConfig.UniqueCodeField, message);
                    return null;
                }

                // 构造 MessageEnvelope
                var envelope = new MessageEnvelope
                {
                    Payload = string.IsNullOrEmpty(content) ? message : $"{{\"_EVENT_DATA\": {content}}}", // 如果没有指定内容字段，使用原始消息
                    Headers = new Dictionary<string, object>
                    {
                        { "Topic", uniqueCode },
                        { "OriginalMessage", message }
                    }
                };

                Log.Debug("成功解析自定义TCP消息: UniqueCode={UniqueCode}, Topic={Topic}",
                    uniqueCode, _eventConfig.EventId);

                return envelope;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "解析自定义TCP消息失败: {Message}", message);
                return null;
            }
        }

        /// <summary>
        /// 从JSON数据中提取指定字段的值
        /// </summary>
        private string ExtractFieldValue(Dictionary<string, object> jsonData, string fieldPath)
        {
            if (string.IsNullOrEmpty(fieldPath) || jsonData == null) return null;

            // 支持嵌套字段，如 "device.id"
            var fieldParts = fieldPath.Split('.');
            object currentValue = jsonData;

            foreach (var part in fieldParts)
            {
                if (currentValue is Dictionary<string, object> dict && dict.TryGetValue(part, out var value))
                {
                    currentValue = value;
                }
                else
                {
                    return null;
                }
            }

            return currentValue?.ToString();
        }

        public override async Task ConnectAsync(CancellationToken cancellationToken = default)
        {
            await _tcpService.StartAsync();
            _isConnected = true;
        }

        public override async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            await _tcpService.StopAsync(cancellationToken);
            _isConnected = false;
        }

        public override async Task PublishAsync(string topic, object message, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var envelope = new MessageEnvelope
            {
                Payload = message,
                Headers = headers ?? new Dictionary<string, object>()
            };
            envelope.Headers["Topic"] = topic;

            var json = JsonHelper.Serialize(envelope);

            // 从 TcpService 直接获取所有在线的客户端，并发地向它们发送消息。
            var onlineClientIds = GetClientIds();
            var sendTasks = new List<Task>(onlineClientIds.Count());

            foreach (var clientId in onlineClientIds)
            {
                if (!_tcpService.TryGetClient(clientId, out var client)) { continue; }
                sendTasks.Add(client.SendAsync(json).ContinueWith(task =>
                {
                    if (task.IsFaulted)
                    {
                        Log.Warning(task.Exception, "Failed to send message to client {ClientId}", client.Id);
                    }
                }, cancellationToken));
            }

            await Task.WhenAll(sendTasks);
        }

        public override async Task PublishAsync(string topic, IEnumerable<object> messages, IDictionary<string, object> headers = null, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            foreach (var message in messages)
            {
                await PublishAsync(topic, message, headers, cancellationToken);
            }
        }

        public override async Task SubscribeAsync(string topic, Func<MessageEnvelope, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var consumer = new TcpMessageConsumer(options, handler);
            var consumers = _consumers.GetOrAdd(topic, _ => new ConcurrentBag<TcpMessageConsumer>());
            consumers.Add(consumer);
            await consumer.StartAsync(cancellationToken);
        }

        public override async Task SubscribeBatchAsync(string topic, Func<List<MessageEnvelope>, CancellationToken, Task> handler, ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            await EnsureConnectedAsync(cancellationToken);

            var consumer = new TcpMessageConsumer(options, handler);
            var consumers = _consumers.GetOrAdd(topic, _ => new ConcurrentBag<TcpMessageConsumer>());
            consumers.Add(consumer);
            await consumer.StartAsync(cancellationToken);
        }

        public override async Task UnsubscribeAsync(string topic, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (_consumers.TryGetValue(topic, out var consumers))
            {
                var consumersToRemove = consumers.ToList();
                consumers.Clear();
                foreach (var consumer in consumersToRemove)
                {
                    await consumer.StopAsync(cancellationToken);
                    await consumer.DisposeAsync();
                }
            }
        }

        protected override async ValueTask DisposeAsyncCore()
        {
            if (IsDisposed) return;

            foreach (var consumers in _consumers.Values)
            {
                foreach (var consumer in consumers)
                {
                    await consumer.DisposeAsync();
                }
            }

            await _tcpService.StopAsync();
            _consumers.Clear();
            _clients.Clear();

            await base.DisposeAsyncCore();
        }
    }

    internal class TcpMessageConsumer : IMessageConsumer
    {
        private readonly Func<MessageEnvelope, CancellationToken, Task> _handler;
        private readonly Func<List<MessageEnvelope>, CancellationToken, Task> _batchHandler;
        private readonly CancellationTokenSource _cts;
        private bool _isRunning;

        public string Name { get; }
        public ConsumerOptions Options { get; }
        public bool IsRunning => _isRunning;

        public TcpMessageConsumer(ConsumerOptions options, Func<MessageEnvelope, CancellationToken, Task> handler)
        {
            Name = options.Name;
            Options = options;
            _handler = handler;
            _cts = new CancellationTokenSource();
        }

        public TcpMessageConsumer(ConsumerOptions options, Func<List<MessageEnvelope>, CancellationToken, Task> batchHandler)
        {
            Name = options.Name;
            Options = options;
            _batchHandler = batchHandler;
            _cts = new CancellationTokenSource();
        }

        public Task StartAsync(CancellationToken cancellationToken = default)
        {
            _isRunning = true;
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken = default)
        {
            _isRunning = false;
            _cts.Cancel();
            return Task.CompletedTask;
        }

        public async ValueTask DisposeAsync()
        {
            await StopAsync();
            _cts.Dispose();
        }

        public async Task HandleMessageAsync(MessageEnvelope envelope, CancellationToken cancellationToken)
        {
            if (!_isRunning) return;
            if (_cts.IsCancellationRequested) return;

            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token, cancellationToken);
            try
            {
                if (_handler != null)
                {
                    await _handler(envelope, linkedCts.Token);
                }
                else if (_batchHandler != null)
                {
                    await _batchHandler([envelope], linkedCts.Token);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "处理消息时发生错误");
            }
        }
    }

    /// <summary>
    /// TCP事件配置
    /// </summary>
    internal class TcpEventConfig
    {
        public string EventId { get; set; }
        public string EventName { get; set; }
        public int Port { get; set; }
        public string UniqueCodeField { get; set; }
        public string ContentField { get; set; }
    }
}