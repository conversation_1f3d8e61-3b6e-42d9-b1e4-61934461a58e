import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { ref } from 'vue';
import EnhancedFunctionList from '../EnhancedFunctionList.vue';

// Mock the function data service
vi.mock('@/services/functionDataService', () => ({
  getFunctionCategories: vi.fn().mockResolvedValue([
    {
      value: 'Common',
      label: '常用函数',
      children: [
        {
          value: 'UUID',
          label: '随机ID',
          script: 'Utils.UUID()',
          remark: '生成随机的UUID',
          category: 'Common',
          categoryDisplayName: '常用函数',
          outputType: 'string',
          parameters: [],
          examples: [
            { title: '基本用法', code: 'Utils.UUID()' },
            { title: '详细示例', code: 'Utils.UUID()' }
          ]
        },
        {
          value: 'NOW',
          label: '当前时间',
          script: 'Utils.NOW()',
          remark: '获取当前时间',
          category: 'Common',
          categoryDisplayName: '常用函数',
          outputType: 'DateTime',
          parameters: [],
          examples: [
            { title: '基本用法', code: 'Utils.NOW()' },
            { title: '详细示例', code: 'Utils.NOW()' }
          ]
        }
      ]
    }
  ])
}));

// Mock TDesign components
vi.mock('tdesign-vue-next', () => ({
  MessagePlugin: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

// Mock other components
vi.mock('@/components/code-preview/index.vue', () => ({
  default: {
    name: 'CodePreview',
    template: '<div>Code Preview</div>'
  }
}));

describe('EnhancedFunctionList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should load function data from backend service', async () => {
    const wrapper = mount(EnhancedFunctionList);
    
    // Wait for component to mount and load data
    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    // Check if function data is loaded
    expect(wrapper.vm.functionData).toBeDefined();
  });

  it('should use backend data for function parameters', async () => {
    const wrapper = mount(EnhancedFunctionList);
    
    // Wait for component to mount
    await wrapper.vm.$nextTick();
    
    // Set an active function with parameters from backend
    const mockFunction = {
      value: 'UUID',
      label: '随机ID',
      script: 'Utils.UUID()',
      remark: '生成随机的UUID',
      parameters: [
        {
          name: 'testParam',
          type: 'string',
          required: true,
          description: '测试参数'
        }
      ]
    };
    
    wrapper.vm.activeFunction = mockFunction;
    await wrapper.vm.$nextTick();
    
    // Check if parameters are correctly displayed
    expect(wrapper.vm.functionParameters).toEqual(mockFunction.parameters);
  });

  it('should use backend data for function examples', async () => {
    const wrapper = mount(EnhancedFunctionList);
    
    // Wait for component to mount
    await wrapper.vm.$nextTick();
    
    // Set an active function with examples from backend
    const mockFunction = {
      value: 'UUID',
      label: '随机ID',
      examples: [
        { title: '基本用法', code: 'Utils.UUID()' },
        { title: '详细示例', code: 'Utils.UUID()' }
      ]
    };
    
    wrapper.vm.activeFunction = mockFunction;
    await wrapper.vm.$nextTick();
    
    // Check if examples are correctly displayed
    expect(wrapper.vm.functionExamples).toEqual(mockFunction.examples);
  });

  it('should get function category from backend data', async () => {
    const wrapper = mount(EnhancedFunctionList);
    
    // Wait for component to mount and load data
    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Test getFunctionCategory function
    const category = wrapper.vm.getFunctionCategory('UUID');
    
    // Should return category based on backend outputType
    expect(category).toBeDefined();
  });

  it('should emit dblclick event when function is selected', async () => {
    const wrapper = mount(EnhancedFunctionList);
    
    const mockFunction = {
      value: 'UUID',
      label: '随机ID',
      script: 'Utils.UUID()',
      remark: '生成随机的UUID'
    };
    
    // Call selectFunction method
    wrapper.vm.selectFunction(mockFunction);
    
    // Check if dblclick event is emitted
    expect(wrapper.emitted('dblclick')).toBeTruthy();
    expect(wrapper.emitted('dblclick')[0]).toEqual([mockFunction]);
  });

  it('should filter functions based on search keyword', async () => {
    const wrapper = mount(EnhancedFunctionList);
    
    // Wait for component to mount and load data
    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Set search keyword
    wrapper.vm.searchKeyword = 'UUID';
    await wrapper.vm.$nextTick();
    
    // Check if filtered data contains only matching functions
    const filteredData = wrapper.vm.filteredData;
    expect(filteredData).toBeDefined();
    
    // Should filter based on the search keyword
    if (filteredData.length > 0) {
      const hasMatchingFunction = filteredData.some(category => 
        category.children && category.children.some(func => 
          func.label.toLowerCase().includes('uuid') || 
          func.value.toLowerCase().includes('uuid')
        )
      );
      expect(hasMatchingFunction).toBe(true);
    }
  });
});
