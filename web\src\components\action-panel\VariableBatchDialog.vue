<template>
  <t-dialog
    v-model:visible="showBatchVariableDialog"
    attach="body"
    header="批量操作变量"
    width="900px"
    @confirm="handleConfirm"
  >
    <t-tabs v-model="tabValue" default-value="json">
      <t-tab-panel value="json" label="JSON">
        <t-row style="margin-top: 8px">
          <t-col :span="12">
            <editor v-model:value="jsonValue" language="json" style="height: 400px"></editor>
          </t-col>
          <!-- <t-col :span="6"></t-col> -->
        </t-row>
      </t-tab-panel>
      <t-tab-panel value="text" label="文本">
        <t-row>
          <t-col :span="6">
            <action-form-title title="变量名"></action-form-title>
            <editor v-model:value="textValue" language="plaintext" style="height: 350px"></editor>
          </t-col>
          <!-- <t-col :span="4">
            <action-form-title title="类型"></action-form-title>
            <editor v-model:value="textTypeValue" language="plaintext" style="height: 250px"></editor>
          </t-col> -->
          <t-col :span="6">
            <action-form-title title="描述"></action-form-title>
            <editor v-model:value="textDescValue" language="plaintext" style="height: 350px"></editor>
          </t-col>
        </t-row>
      </t-tab-panel>
    </t-tabs>
  </t-dialog>
</template>
<script lang="ts">
export default {
  name: 'VariableBatchDialog',
};
</script>
<script setup lang="ts">
import { cloneDeep } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { ref, watch } from 'vue';

import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import Editor from '@/components/editor/index.vue';
import CustomError from '@/utils/exception/CustomError';

import { FlowData } from './model';
import { getRandomId } from './utils';

const actionFlowStore = useActionFlowStore();
const { currentVariableData, showBatchVariableDialog } = storeToRefs(actionFlowStore);

// 是否合并变量
// const isMerge = ref(false);
const jsonValue = ref('');
const textValue = ref('');
// const textTypeValue = ref('');
const textDescValue = ref('');

watch(
  showBatchVariableDialog,
  (val) => {
    if (!val) return;
    const json = flowDataToJson(currentVariableData.value);
    jsonValue.value = JSON.stringify(json, null, 2);

    textValue.value = flowDataToText(currentVariableData.value, 'key');
    textDescValue.value = flowDataToText(currentVariableData.value, 'description');
  },
  {
    deep: true,
  },
);

const flowDataToJson = (flowData: FlowData) => {
  if (!flowData) return null;

  if (flowData.type === 'object') {
    const objectResult = {};
    for (const child of flowData.children || []) {
      objectResult[child.key?.toString().trim()] = flowDataToJson(child);
    }
    return objectResult;
  }
  if (flowData.type === 'array') {
    const arrayResult = [];
    const objectResult = {};
    for (const child of flowData.children || []) {
      objectResult[child.key?.toString().trim()] = flowDataToJson(child);
    }
    arrayResult.push(objectResult);

    return arrayResult;
  }
  if (flowData.value && flowData.value.type === 'text') {
    return flowData.value.textValue;
  }
  return typeToDefault(flowData.value?.dataType || flowData.type);
};

const flowDataToText = (flowData: FlowData, keyCode: string, indentLevel = 0) => {
  if (!flowData) return '';

  let result = '';

  if (flowData.type === 'object' || flowData.type === 'array') {
    for (const child of flowData.children || []) {
      const indent = ' '.repeat(indentLevel * 4); // 每一层增加4个空格
      result += `${indent}${child[keyCode]?.toString().replaceAll('\r\n', ' ').replaceAll('\n', ' ').trim() || ''}\n`;
      result += flowDataToText(child, keyCode, indentLevel + 1);
    }
  }

  return result;
};

const typeToDefault = (type) => {
  switch (type) {
    case 'object':
      return {};
    case 'array':
      return [];
    case 'char':
    case 'string':
      return '';
    case 'bool':
      return false;
    case 'decimal':
    case 'int':
    case 'short':
    case 'long':
    case 'double':
    case 'float':
    case 'number':
      return 0;
    case 'Date':
      return '2024-01-01';
    case 'DateTime':
      return '2024-01-01 12:00:00';
    default:
      break;
  }
  return null;
};

const valueToType = (obj) => {
  let type = 'string'; // 默认为字符串
  switch (typeof obj) {
    case 'string':
      type = 'string';
      break;
    case 'number':
      if (Number.isInteger(obj)) {
        type = 'int'; // 如果是整数
      } else {
        type = 'decimal'; // 如果是小数
      }
      break;
    case 'boolean':
      type = 'bool';
      break;
    case 'object':
      if (obj instanceof Array) {
        type = 'array';
      } else if (obj instanceof Date) {
        type = 'DateTime';
      } else {
        type = 'object';
      }
      break;
    default:
      type = 'string'; // 默认为字符串
      break;
  }
  return type;
};

function jsonToFlowData(jsonString: string, children: FlowData[]) {
  try {
    const json = JSON.parse(jsonString);
    parseJsonToFlowData(json, children);
  } catch (error) {
    throw new CustomError('JSON 格式错误，请检查输入', 400, 'VariableBatchDialog');
  }
}

function parseJsonToFlowData(obj: any, children: FlowData[]) {
  if (Array.isArray(obj)) {
    if (obj.length === 0) return;
    parseJsonToFlowData(obj[0], children);
  } else if (typeof obj === 'object') {
    if (obj === null) return;
    Object.entries(obj).forEach(([key, value]) => {
      const item = children.find((item) => item.key === key);
      const type = valueToType(value);
      const isObj = type === 'object' || type === 'array';
      if (item) {
        if (isObj) {
          item.type = type;
          item.children = item.children || [];
          parseJsonToFlowData(value, item.children);
        }
      } else {
        const newItem: FlowData = {
          id: getRandomId(),
          key,
          type,
          value: null,
          children: isObj ? [] : null,
        };
        if (isObj) {
          parseJsonToFlowData(value, newItem.children);
        }
        children.push(newItem);
      }
    });
  }
}

const getIndentLevel = (line: string): number => {
  const match = line.match(/^\s+/);
  return Math.round((match ? match[0].length : 0) / 4);
};

const textToFlowData = (text: string, desc: string, children: FlowData[]) => {
  const lines = text.split('\n');
  const descLines = desc.split('\n');
  if (lines.length !== descLines.length) {
    throw new CustomError('变量名和描述的行数不一致', 400, 'VariableBatchDialog');
  }
  const levelData = {};
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const indentLevel = getIndentLevel(line);

    const descLine = descLines[i].trim();
    const key = line.trim();
    if (key === '') continue;
    let item = children.find((item) => item.key === key);
    if (!item) {
      const parent = levelData[indentLevel - 1];
      if (parent) {
        parent.children = parent.children || [];
        children = parent.children;
      }
      item = children.find((item) => item.key === key);
    }

    if (item) {
      levelData[indentLevel] = item;
      if (descLine !== '') {
        item.description = descLine;
      }
    } else {
      const newItem: FlowData = {
        id: getRandomId(),
        key,
        type: 'string',
        value: null,
        children: null,
      };
      if (descLine !== '') {
        newItem.description = descLine;
      }
      children.push(newItem);
      levelData[indentLevel] = newItem; // 更新 levelData
    }
  }
};

const tabValue = ref('json');
const handleConfirm = () => {
  try {
    const children = cloneDeep(currentVariableData.value.children || []);
    if (tabValue.value === 'json') {
      jsonToFlowData(jsonValue.value, children);
    } else if (tabValue.value === 'text') {
      textToFlowData(textValue.value, textDescValue.value, children);
    }

    currentVariableData.value.children = children;
    showBatchVariableDialog.value = false;
    actionFlowStore.isSaveBatchVariable = true;
  } catch (error) {
    console.error('批量更新变量失败:', error);
    // 如果是自定义错误，显示错误信息
    if (error instanceof CustomError) {
      alert(error.message);
    } else {
      alert('批量更新变量失败，请检查输入格式');
    }
  }
};
</script>
<style lang="less" scoped></style>
