using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.ScriptExtensions;
using System.Text;
using System.Text.Json;
using System.Linq.Expressions;
using System.Reflection;

namespace GCP.Functions.Common.VisualFunction
{
    /// <summary>
    /// 可视化函数执行引擎
    /// </summary>
    public class VisualFunctionEngine
    {
        private readonly DbContext _db;
        private readonly JavascriptUtils _utils;
        private readonly VisualFunctionExpressionEngine _expressionEngine;

        public VisualFunctionEngine(DbContext db = null)
        {
            _db = db;
            _utils = new JavascriptUtils(db);
            _expressionEngine = new VisualFunctionExpressionEngine(_utils);
        }

        /// <summary>
        /// 执行可视化函数编排
        /// </summary>
        /// <param name="steps">函数步骤列表</param>
        /// <param name="inputData">输入数据</param>
        /// <param name="useCompiledExpression">是否使用编译表达式（默认true，提高性能）</param>
        /// <returns>执行结果</returns>
        public async Task<object> ExecuteAsync(List<VisualFunctionStep> steps, Dictionary<string, object> inputData, bool useCompiledExpression = true)
        {
            return await ExecuteWithDetailsAsync(steps, inputData, useCompiledExpression);
        }

        /// <summary>
        /// 执行可视化函数编排并返回详细信息
        /// </summary>
        /// <param name="steps">函数步骤列表</param>
        /// <param name="inputData">输入数据</param>
        /// <param name="useCompiledExpression">是否使用编译表达式（默认true，提高性能）</param>
        /// <returns>执行结果</returns>
        public async Task<object> ExecuteWithDetailsAsync(List<VisualFunctionStep> steps, Dictionary<string, object> inputData, bool useCompiledExpression = true)
        {
            if (steps == null || steps.Count == 0)
                return inputData;

            // 如果启用表达式编译且所有步骤都是内置函数，使用编译执行
            if (useCompiledExpression && steps.All(s => s.FunctionType == VisualFunctionType.Builtin))
            {
                try
                {
                    return ExecuteCompiledExpression(steps, inputData);
                }
                catch (Exception ex)
                {
                    // 如果编译执行失败，回退到解释执行
                    Console.WriteLine($"编译执行失败，回退到解释执行: {ex.Message}");
                }
            }

            // 解释执行（原有逻辑）
            return await ExecuteInterpretedAsync(steps, inputData);
        }

        /// <summary>
        /// 解释执行（原有逻辑）
        /// </summary>
        private async Task<object> ExecuteInterpretedAsync(List<VisualFunctionStep> steps, Dictionary<string, object> inputData)
        {
            var context = new VisualFunctionContext
            {
                InputData = inputData ?? new Dictionary<string, object>(),
                Variables = new Dictionary<string, object>(),
                StepResults = new Dictionary<string, object>()
            };

            return await ExecuteInterpretedWithDetailsAsync(steps, context);
        }

        /// <summary>
        /// 解释执行并收集详细步骤信息
        /// </summary>
        public async Task<object> ExecuteInterpretedWithDetailsAsync(List<VisualFunctionStep> steps, VisualFunctionContext context)
        {


            // 按顺序执行每个步骤
            foreach (var step in steps.OrderBy(s => s.Order))
            {

                var stepStopwatch = System.Diagnostics.Stopwatch.StartNew();
                try
                {
                    var result = await ExecuteStepAsync(step, context);


                    // 保存步骤结果
                    var resultKey = !string.IsNullOrEmpty(step.OutputVariable)
                        ? step.OutputVariable
                        : $"step_{step.Order}_result";

                    context.StepResults[resultKey] = result;
                    context.Variables[resultKey] = result;

                    // 记录步骤执行详情
                    stepStopwatch.Stop();
                    var stepResult = new VisualFunctionStepResult
                    {
                        StepId = step.Id,
                        StepName = step.DisplayName,
                        Result = result,
                        ExecutionTimeMs = stepStopwatch.ElapsedMilliseconds,
                        Success = true,
                        InputData = GetStepInputData(step, context),
                        OutputVariable = resultKey
                    };
                    context.StepExecutionDetails.Add(stepResult);

                }
                catch (Exception ex)
                {
                    stepStopwatch.Stop();
                    // 记录失败的步骤详情
                    context.StepExecutionDetails.Add(new VisualFunctionStepResult
                    {
                        StepId = step.Id,
                        StepName = step.DisplayName,
                        Success = false,
                        ErrorMessage = ex.Message,
                        ExecutionTimeMs = stepStopwatch.ElapsedMilliseconds,
                        InputData = GetStepInputData(step, context)
                    });

                    throw new CustomException($"执行步骤 {step.Order + 1} ({step.DisplayName}) 时发生错误: {ex.Message}", ex);
                }
            }

            // 返回最后一步的结果，如果没有步骤则返回输入数据
            return context.StepResults.Values.LastOrDefault() ?? context.InputData;
        }

        /// <summary>
        /// 执行单个函数步骤
        /// </summary>
        private async Task<object> ExecuteStepAsync(VisualFunctionStep step, VisualFunctionContext context)
        {
            // 准备参数
            var parameters = new List<object>();
            
            foreach (var param in step.Parameters)
            {
                var paramValue = ResolveParameterValue(param, context);
                parameters.Add(paramValue);
            }

            // 根据函数类型执行
            return step.FunctionType switch
            {
                VisualFunctionType.CSharp => await ExecuteCSharpFunctionAsync(step.FunctionName, parameters.ToArray()),
                VisualFunctionType.JavaScript => await ExecuteJavaScriptFunctionAsync(step.FunctionName, parameters.ToArray()),
                VisualFunctionType.Builtin => ExecuteBuiltinFunction(step.FunctionName, parameters.ToArray()),
                _ => throw new NotSupportedException($"不支持的函数类型: {step.FunctionType}")
            };
        }

        /// <summary>
        /// 解析参数值
        /// </summary>
        private object ResolveParameterValue(VisualFunctionParameter param, VisualFunctionContext context)
        {
            return param.Type switch
            {
                VisualParameterType.Text => param.Value,
                VisualParameterType.Variable => ResolveVariableValue(param.Value?.ToString(), context),
                VisualParameterType.PreviousResult => ResolvePreviousResult(param.Value?.ToString(), context),
                _ => param.Value
            };
        }

        /// <summary>
        /// 解析变量值
        /// </summary>
        private object ResolveVariableValue(string variablePath, VisualFunctionContext context)
        {
            if (string.IsNullOrEmpty(variablePath))
                return null;

            // 处理 _data.xxx.xxx 格式的变量路径
            if (variablePath.StartsWith("_data."))
            {
                var path = variablePath.Substring(6); // 移除 "_data." 前缀
                return GetValueByPath(context.InputData, path);
            }

            // 处理直接变量名
            if (context.Variables.TryGetValue(variablePath, out var value))
                return value;

            if (context.InputData.TryGetValue(variablePath, out var inputValue))
                return inputValue;

            return null;
        }

        /// <summary>
        /// 解析上一步结果
        /// </summary>
        private object ResolvePreviousResult(string resultKey, VisualFunctionContext context)
        {
            if (string.IsNullOrEmpty(resultKey))
                return null;

            return context.StepResults.TryGetValue(resultKey, out var result) ? result : null;
        }

        /// <summary>
        /// 根据路径获取值
        /// </summary>
        private object GetValueByPath(object data, string path)
        {
            if (data == null || string.IsNullOrEmpty(path))
                return null;

            var parts = path.Split('.');
            var current = data;

            foreach (var part in parts)
            {
                if (current is Dictionary<string, object> dict)
                {
                    if (!dict.TryGetValue(part, out current))
                        return null;
                }
                else if (current is JsonElement element)
                {
                    if (element.ValueKind == JsonValueKind.Object && element.TryGetProperty(part, out var prop))
                        current = prop;
                    else
                        return null;
                }
                else
                {
                    // 使用反射获取属性值
                    var property = current.GetType().GetProperty(part);
                    if (property != null)
                        current = property.GetValue(current);
                    else
                        return null;
                }
            }

            return current;
        }

        /// <summary>
        /// 执行内置函数
        /// </summary>
        private object ExecuteBuiltinFunction(string functionName, object[] parameters)
        {
            // 使用反射调用 JavascriptUtils 中的方法
            var method = typeof(JavascriptUtils).GetMethod(functionName);
            if (method == null)
                throw new CustomException($"未找到内置函数: {functionName}");

            try
            {
                return method.Invoke(_utils, parameters);
            }
            catch (Exception ex)
            {
                throw new CustomException($"执行内置函数 {functionName} 时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 执行C#函数（预留接口）
        /// </summary>
        private async Task<object> ExecuteCSharpFunctionAsync(string functionName, object[] parameters)
        {
            // 这里可以集成现有的C#函数执行机制
            await Task.CompletedTask;
            throw new NotImplementedException("C#函数执行功能待实现");
        }

        /// <summary>
        /// 执行JavaScript函数（预留接口）
        /// </summary>
        private async Task<object> ExecuteJavaScriptFunctionAsync(string functionName, object[] parameters)
        {
            // 这里可以集成现有的JavaScript函数执行机制
            await Task.CompletedTask;
            throw new NotImplementedException("JavaScript函数执行功能待实现");
        }

        /// <summary>
        /// 获取步骤的输入数据（用于调试和日志）
        /// </summary>
        private Dictionary<string, object> GetStepInputData(VisualFunctionStep step, VisualFunctionContext context)
        {
            var inputData = new Dictionary<string, object>();

            foreach (var param in step.Parameters)
            {
                var value = ResolveParameterValue(param, context);
                inputData[param.Name] = value;
            }

            return inputData;
        }

        /// <summary>
        /// 使用编译表达式执行函数链
        /// </summary>
        private object ExecuteCompiledExpression(List<VisualFunctionStep> steps, Dictionary<string, object> inputData)
        {
            return _expressionEngine.Execute(steps, inputData);
        }



        /// <summary>
        /// 生成C#表达式代码
        /// </summary>
        public string GenerateCSharpExpression(List<VisualFunctionStep> steps)
        {
            if (steps == null || steps.Count == 0)
                return "return inputData;";

            var sb = new StringBuilder();
            sb.AppendLine("// 自动生成的可视化函数表达式");
            sb.AppendLine("var utils = new JavascriptUtils(db);");
            sb.AppendLine();

            foreach (var step in steps.OrderBy(s => s.Order))
            {
                var resultVar = !string.IsNullOrEmpty(step.OutputVariable) 
                    ? step.OutputVariable 
                    : $"step_{step.Order}_result";

                sb.AppendLine($"// 步骤 {step.Order + 1}: {step.DisplayName}");
                
                var paramExpressions = step.Parameters.Select(p => GenerateParameterExpression(p)).ToArray();
                var paramString = string.Join(", ", paramExpressions);
                
                sb.AppendLine($"var {resultVar} = utils.{step.FunctionName}({paramString});");
                sb.AppendLine();
            }

            var lastStep = steps.OrderBy(s => s.Order).Last();
            var lastResultVar = !string.IsNullOrEmpty(lastStep.OutputVariable) 
                ? lastStep.OutputVariable 
                : $"step_{lastStep.Order}_result";

            sb.AppendLine($"return {lastResultVar};");

            return sb.ToString();
        }

        /// <summary>
        /// 生成参数表达式
        /// </summary>
        private string GenerateParameterExpression(VisualFunctionParameter param)
        {
            return param.Type switch
            {
                VisualParameterType.Text => $"\"{param.Value}\"",
                VisualParameterType.Variable => param.Value?.ToString() ?? "null",
                VisualParameterType.PreviousResult => param.Value?.ToString() ?? "null",
                _ => "null"
            };
        }
    }

    /// <summary>
    /// 可视化函数执行上下文
    /// </summary>
    public class VisualFunctionContext
    {
        public Dictionary<string, object> InputData { get; set; } = new();
        public Dictionary<string, object> Variables { get; set; } = new();
        public Dictionary<string, object> StepResults { get; set; } = new();
        public List<VisualFunctionStepResult> StepExecutionDetails { get; set; } = new();
    }
}
