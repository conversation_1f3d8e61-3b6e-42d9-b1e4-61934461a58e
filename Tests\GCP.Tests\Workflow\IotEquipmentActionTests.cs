using Xunit;
using Xunit.Abstractions;
using FluentAssertions;
using GCP.Tests.Infrastructure;
using GCP.FunctionPool.Flow.Services;
using GCP.FunctionPool.Flow.Models;
using GCP.DataAccess;
using GCP.Common;
using GCP.Iot.Models;
using GCP.Iot.Services;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using GCP.Functions.Common;

namespace GCP.Tests.Workflow
{
    /// <summary>
    /// 设备测点动作测试类
    /// </summary>
    public class IotEquipmentActionTests : DatabaseTestBase
    {
        private Mock<EquipmentCommunicationManager> _mockCommunicationManager;
        private Mock<EquipmentCommunicationTask> _mockEquipmentTask;

        public IotEquipmentActionTests(ITestOutputHelper output) : base(output)
        {
            _mockCommunicationManager = new Mock<EquipmentCommunicationManager>();
            _mockEquipmentTask = new Mock<EquipmentCommunicationTask>();
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);
            services.AddScoped<DataIotEquipment>();
            
            // 注册Mock服务
            services.AddSingleton(_mockCommunicationManager.Object);
        }

        #region 设备测点写入测试

        [Fact]
        public async Task IotVariableWrite_WithValidData_ShouldWriteSuccessfully()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataIotEquipment = GetService<DataIotEquipment>();
            SetTestContext(dataIotEquipment);

            // 设置Mock行为
            var writeResult = new DriverOperationResult { Status = OperationStatus.Success };
            _mockEquipmentTask.Setup(x => x.WriteVariableAsync("D100", 123, DataTypeEnum.Int16))
                             .ReturnsAsync(writeResult);
            _mockCommunicationManager.Setup(x => x.GetEquipmentTask("device001"))
                                   .Returns(_mockEquipmentTask.Object);

            var writeData = new DataIotVariableWrite
            {
                Name = "测试设备写入",
                Description = "写入设备测点值",
                EquipmentId = new DataValue { Type = "text", TextValue = "device001" },
                Address = new DataValue { Type = "text", TextValue = "D100" },
                Value = new DataValue { Type = "text", TextValue = "123" },
            };

            // Act
            await dataIotEquipment.WriteVariable(writeData);

            // Assert
            _mockEquipmentTask.Verify(x => x.WriteVariableAsync("D100", 123, DataTypeEnum.Int16), Times.Once);
            Output.WriteLine("设备测点写入测试成功");
        }

        [Fact]
        public async Task IotVariableWrite_WithInvalidEquipment_ShouldThrowException()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataIotEquipment = GetService<DataIotEquipment>();
            SetTestContext(dataIotEquipment);

            // 设置Mock行为 - 返回null表示设备不存在
            _mockCommunicationManager.Setup(x => x.GetEquipmentTask("invalid_device"))
                                   .Returns((EquipmentCommunicationTask)null);

            var writeData = new DataIotVariableWrite
            {
                Name = "测试无效设备",
                Description = "写入不存在的设备",
                EquipmentId = new DataValue { Type = "text", TextValue = "invalid_device" },
                Address = new DataValue { Type = "text", TextValue = "D100" },
                Value = new DataValue { Type = "text", TextValue = "123" },
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<CustomException>(() => dataIotEquipment.WriteVariable(writeData));
            exception.Message.Should().Contain("未找到设备通信任务");
        }

        [Fact]
        public async Task IotVariableWrite_WithWriteFailure_ShouldThrowException()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataIotEquipment = GetService<DataIotEquipment>();
            SetTestContext(dataIotEquipment);

            // 设置Mock行为 - 写入失败
            var writeResult = new DriverOperationResult
            {
                Status = OperationStatus.Failed,
                ErrorMessage = "设备通信超时"
            };
            _mockEquipmentTask.Setup(x => x.WriteVariableAsync("D100", 123, DataTypeEnum.Int16))
                             .ReturnsAsync(writeResult);
            _mockCommunicationManager.Setup(x => x.GetEquipmentTask("device001"))
                                   .Returns(_mockEquipmentTask.Object);

            var writeData = new DataIotVariableWrite
            {
                Name = "测试写入失败",
                Description = "模拟设备写入失败",
                EquipmentId = new DataValue { Type = "text", TextValue = "device001" },
                Address = new DataValue { Type = "text", TextValue = "D100" },
                Value = new DataValue { Type = "text", TextValue = "123" },
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<CustomException>(() => dataIotEquipment.WriteVariable(writeData));
            exception.Message.Should().Contain("设备通信超时");
        }

        #endregion

        #region 设备测点读取测试

        [Fact]
        public async Task IotVariableRead_WithValidData_ShouldReturnValue()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataIotEquipment = GetService<DataIotEquipment>();
            SetTestContext(dataIotEquipment);

            // 设置Mock行为
            _mockEquipmentTask.Setup(x => x.GetVariableValue("Temperature"))
                             .Returns(25.5);
            _mockCommunicationManager.Setup(x => x.GetEquipmentTask("device001"))
                                   .Returns(_mockEquipmentTask.Object);

            var readData = new DataIotVariableRead
            {
                Name = "测试设备读取",
                Description = "读取设备测点值",
                EquipmentId = new DataValue { Type = "text", TextValue = "device001" },
                VariableName = new DataValue { Type = "text", TextValue = "Temperature" }
            };

            // Act
            var result = await dataIotEquipment.ReadVariable(readData);

            // Assert
            result.Should().Be(25.5, "应该返回设备测点的值");
            _mockEquipmentTask.Verify(x => x.GetVariableValue("Temperature"), Times.Once);
            Output.WriteLine($"设备测点读取成功: {result}");
        }

        [Fact]
        public async Task IotVariableRead_WithInvalidEquipment_ShouldThrowException()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataIotEquipment = GetService<DataIotEquipment>();
            SetTestContext(dataIotEquipment);

            // 设置Mock行为 - 返回null表示设备不存在
            _mockCommunicationManager.Setup(x => x.GetEquipmentTask("invalid_device"))
                                   .Returns((EquipmentCommunicationTask)null);

            var readData = new DataIotVariableRead
            {
                Name = "测试无效设备读取",
                Description = "读取不存在的设备",
                EquipmentId = new DataValue { Type = "text", TextValue = "invalid_device" },
                VariableName = new DataValue { Type = "text", TextValue = "Temperature" }
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<CustomException>(() => dataIotEquipment.ReadVariable(readData));
            exception.Message.Should().Contain("未找到设备通信任务");
        }

        #endregion

        #region 设备所有参数读取测试

        [Fact]
        public async Task IotEquipmentRead_WithValidData_ShouldReturnAllValues()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataIotEquipment = GetService<DataIotEquipment>();
            SetTestContext(dataIotEquipment);

            // 设置Mock行为
            var allValues = new Dictionary<string, object>
            {
                ["Temperature"] = 25.5,
                ["Humidity"] = 60.2,
                ["Pressure"] = 1013.25
            };
            _mockEquipmentTask.Setup(x => x.GetCurrentValues())
                             .Returns(allValues);
            _mockCommunicationManager.Setup(x => x.GetEquipmentTask("device001"))
                                   .Returns(_mockEquipmentTask.Object);

            var readData = new DataIotEquipmentRead
            {
                Name = "测试设备全参数读取",
                Description = "读取设备所有参数",
                EquipmentId = new DataValue { Type = "text", TextValue = "device001" }
            };

            // Act
            var result = await dataIotEquipment.ReadEquipmentAllVariables(readData);

            // Assert
            result.Should().NotBeNull("应该返回参数字典");
            result.Should().HaveCount(3, "应该包含3个参数");
            result["Temperature"].Should().Be(25.5);
            result["Humidity"].Should().Be(60.2);
            result["Pressure"].Should().Be(1013.25);
            
            _mockEquipmentTask.Verify(x => x.GetCurrentValues(), Times.Once);
            Output.WriteLine($"设备全参数读取成功，共{result.Count}个参数");
        }

        #endregion

        #region 批量设备参数读取测试

        [Fact]
        public async Task IotMultiEquipmentRead_WithValidData_ShouldReturnAllDeviceValues()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataIotEquipment = GetService<DataIotEquipment>();
            SetTestContext(dataIotEquipment);

            // 设置Mock行为
            var device1Values = new Dictionary<string, object> { ["Temperature"] = 25.5 };
            var device2Values = new Dictionary<string, object> { ["Pressure"] = 1013.25 };

            var mockTask1 = new Mock<EquipmentCommunicationTask>();
            var mockTask2 = new Mock<EquipmentCommunicationTask>();

            mockTask1.Setup(x => x.GetCurrentValues()).Returns(device1Values);
            mockTask2.Setup(x => x.GetCurrentValues()).Returns(device2Values);

            _mockCommunicationManager.Setup(x => x.GetEquipmentTask("device001"))
                                   .Returns(mockTask1.Object);
            _mockCommunicationManager.Setup(x => x.GetEquipmentTask("device002"))
                                   .Returns(mockTask2.Object);

            var readData = new DataIotMultiEquipmentRead
            {
                Name = "测试批量设备读取",
                Description = "批量读取多个设备参数",
                EquipmentIds = new DataValue 
                { 
                    Type = "script", 
                    ScriptValue = "['device001', 'device002']" 
                }
            };

            // Act
            var result = await dataIotEquipment.ReadMultiEquipmentVariables(readData);

            // Assert
            result.Should().NotBeNull("应该返回设备参数字典");
            result.Should().HaveCount(2, "应该包含2个设备的数据");
            result["device001"]["Temperature"].Should().Be(25.5);
            result["device002"]["Pressure"].Should().Be(1013.25);
            
            Output.WriteLine($"批量设备读取成功，共{result.Count}个设备");
        }

        #endregion

        /// <summary>
        /// 设置测试上下文
        /// </summary>
        private void SetTestContext(DataIotEquipment service)
        {
            var context = new FunctionContext
            {
                globalData = new Dictionary<string, object>(),
                Args = new Dictionary<string, object>(),
                Persistence = false,
                trackId = TUID.NewTUID().ToString()
            };

            var dbContext = GetService<IDbContext>();
            context.LocalDbContext.Value = dbContext;

            // 初始化Current属性
            context.Current = new FunctionProvider
            {
                FunctionName = "测试设备动作",
                Level = 0,
                SeqNo = 0,
                IsFlow = false
            };

            // 通过反射设置Context属性
            var contextProperty = typeof(DataIotEquipment).GetProperty("Context");
            contextProperty?.SetValue(service, context);
        }
    }


}
