using Xunit;
using Xunit.Abstractions;
using FluentAssertions;
using GCP.Tests.Infrastructure;
using GCP.FunctionPool.Flow.Services;
using GCP.FunctionPool.Flow.Models;
using GCP.DataAccess;
using GCP.Common;
using LinqToDB;
using Microsoft.Extensions.DependencyInjection;
using EasyCaching.Core;

namespace GCP.Tests.Workflow
{
    /// <summary>
    /// 工作流动作测试类
    /// </summary>
    public class WorkflowActionTests : DatabaseTestBase
    {
        public WorkflowActionTests(ITestOutputHelper output) : base(output)
        {
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);
            services.AddScoped<DataControl>();
            services.AddScoped<DataQuery>();
            services.AddScoped<DataSave>();
            services.AddScoped<ApiRequest>();
            services.AddScoped<JobControl>();
            services.AddScoped<DataCache>();
            services.AddScoped<DataIotEquipment>();

            // 配置内存缓存用于测试
            services.AddEasyCaching(options =>
            {
                options.UseInMemory("test-cache");
            });
        }

        #region 控制动作测试

        [Fact]
        public async Task ControlDelay_ShouldDelayExecution()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataControl = GetService<DataControl>();
            SetTestContext(dataControl);

            var delayData = new DataDelay
            {
                Name = "测试延迟",
                Description = "延迟100毫秒",
                MillisecondsDelay = new DataValue { Type = "text", TextValue = "100" }
            };

            var startTime = DateTime.Now;

            // Act
            await dataControl.Delay(delayData);
            var endTime = DateTime.Now;

            // Assert
            var duration = (endTime - startTime).TotalMilliseconds;
            duration.Should().BeGreaterThanOrEqualTo(90, "延迟时间应该至少90毫秒");
            duration.Should().BeLessThanOrEqualTo(300, "延迟时间不应该超过300毫秒");
            Output.WriteLine($"实际延迟时间: {duration}毫秒");
        }

        [Fact]
        public async Task DataBranch_WithTrueCondition_ShouldReturnTrue()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataControl = GetService<DataControl>();
            SetTestContext(dataControl);

            var branchData = new DataBranch
            {
                Name = "测试分支",
                Description = "条件判断",
                Type = "script",
                Script = "true"
            };

            // Act
            var result = dataControl.BranchData(branchData);

            // Assert
            result.Should().BeTrue("脚本返回true时分支应该返回true");
        }

        [Fact]
        public async Task DataBranch_WithFalseCondition_ShouldReturnFalse()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataControl = GetService<DataControl>();
            SetTestContext(dataControl);

            var branchData = new DataBranch
            {
                Name = "测试分支",
                Description = "条件判断",
                Type = "script",
                Script = "false"
            };

            // Act
            var result = dataControl.BranchData(branchData);

            // Assert
            result.Should().BeFalse("脚本返回false时分支应该返回false");
        }

        [Fact]
        public async Task DataForEach_WithArray_ShouldReturnArrayList()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataControl = GetService<DataControl>();
            SetTestContext(dataControl);

            var forEachData = new DataForEach
            {
                Name = "测试迭代",
                Description = "迭代数组",
                DataSource = new DataValue { Type = "script", ScriptValue = "[1, 2, 3]" }
            };

            // Act
            var result = dataControl.ForEachData(forEachData);

            // Assert
            result.Should().NotBeNull("迭代结果不应该为空");
            result.Should().BeAssignableTo<System.Collections.ArrayList>("结果应该是ArrayList类型");
            var arrayList = result as System.Collections.ArrayList;
            arrayList!.Count.Should().Be(3, "数组应该包含3个元素");
        }

        [Fact]
        public async Task RunScript_ShouldExecuteJavaScript()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataControl = GetService<DataControl>();
            SetTestContext(dataControl);

            var scriptData = new DataScript
            {
                Name = "测试脚本",
                Description = "执行JavaScript",
                Script = "1 + 2"
            };

            // Act
            var result = dataControl.RunScript(scriptData);

            // Assert
            result.Should().NotBeNull("脚本执行结果不应该为空");
            result.Should().Be(3, "1 + 2 应该等于 3");
        }

        #endregion

        #region 数据库动作测试

        [Fact]
        public async Task DataQuery_WithSimpleSQL_ShouldReturnData()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataQuery = GetService<DataQuery>();
            SetTestContext(dataQuery);

            var queryData = new DataQueryData
            {
                Name = "测试查询",
                DataSource = "test-datasource-001",
                OperateType = "sql",
                SqlInfo = new SqlInfo
                {
                    Sql = "SELECT 1 as id, 'test' as name",
                    Parameters = new List<SqlParamInfo>()
                },
                IsPaging = false,
                AutoPaged = false
            };

            // Act
            var result = await dataQuery.QueryData(queryData);

            // Assert
            result.Should().NotBeNull("查询结果不应该为空");
            Output.WriteLine($"查询结果: {JsonHelper.Serialize(result)}");
        }

        [Fact]
        public async Task DataSave_WithTestData_ShouldSaveSuccessfully()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataSave = GetService<DataSave>();
            SetTestContext(dataSave);

            var saveData = new DataSaveData
            {
                Name = "测试保存",
                DataSource = "test-datasource-001",
                OperateType = DataSaveOperationType.Insert,
                SourceDataPath = new DataValue { Type = "script", ScriptValue = "[{id: 1, name: 'test'}]" },
                ConfigureInfo = new DataSourceTableData
                {
                    TableName = "test_table",
                    Columns = new List<ColumnInfo>
                    {
                        new ColumnInfo { ColumnName = "id", DataType = "int", IsPrimaryKey = true, IsCondition = true },
                        new ColumnInfo { ColumnName = "name", DataType = "varchar" }
                    }
                }
            };

            // Act
            var action = async () => await dataSave.SaveData(saveData);

            // Assert
            await action.Should().NotThrowAsync("保存数据不应该抛出异常");
        }

        #endregion

        #region API动作测试

        [Fact]
        public async Task ApiRequest_WithValidApi_ShouldMakeRequest()
        {
            // Arrange
            await InitializeTestDataAsync();
            var apiRequest = GetService<ApiRequest>();
            SetTestContext(apiRequest);

            var requestData = new DataApiRequest
            {
                Name = "测试API请求",
                ApiId = "test-api-001",
                Inputs = new List<FlowData>()
            };

            // Act & Assert
            // 由于需要真实的API配置，这里主要测试方法不抛出配置错误
            var action = async () => await apiRequest.RequestApi(requestData);
            
            // 可能会抛出业务异常（如API不存在），但不应该是系统异常
            await action.Should().NotThrowAsync<NullReferenceException>("不应该抛出空引用异常");
            await action.Should().NotThrowAsync<InvalidOperationException>("不应该抛出无效操作异常");
        }

        #endregion

        #region 作业控制动作测试

        [Fact]
        public async Task ChangeNextBeginTime_ShouldUpdateJobTime()
        {
            // Arrange
            await InitializeTestDataAsync();
            var jobControl = GetService<JobControl>();
            SetTestContext(jobControl);

            var nextTime = DateTime.Now.AddHours(1);
            var changeTimeData = new JobChangeNextBeginTimeData
            {
                Name = "修改下次执行时间",
                NextBeginTime = new DataValue { Type = "text", TextValue = nextTime.ToString("yyyy-MM-dd HH:mm:ss") }
            };

            // Act
            var action = () => jobControl.ChangeNextBeginTime(changeTimeData);

            // Assert
            action.Should().NotThrow("修改下次执行时间不应该抛出异常");
        }

        #endregion

        /// <summary>
        /// 设置测试上下文
        /// </summary>
        private void SetTestContext(DataBaseService service)
        {
            var context = new FunctionContext
            {
                globalData = new Dictionary<string, object>(),
                Args = new Dictionary<string, object>(),
                Persistence = false,
                trackId = TUID.NewTUID().ToString()
            };

            var dbContext = GetService<IDbContext>();
            context.LocalDbContext.Value = dbContext;

            // 初始化Current属性
            context.Current = new FunctionProvider
            {
                FunctionName = "测试动作",
                Level = 0,
                SeqNo = 0,
                IsFlow = false
            };

            // 通过反射设置Context属性
            var contextProperty = typeof(DataBaseService).GetProperty("Context");
            contextProperty?.SetValue(service, context);

            // 设置解决方案和项目ID
            var solutionIdProperty = typeof(DataBaseService).BaseType?.GetProperty("SolutionId");
            var projectIdProperty = typeof(DataBaseService).BaseType?.GetProperty("ProjectId");

            solutionIdProperty?.SetValue(service, "test-solution-001");
            projectIdProperty?.SetValue(service, "test-project-001");
        }

        /// <summary>
        /// 获取SQLite连接字符串
        /// </summary>
        private string GetSqliteConnectionString(GcpDb db)
        {
            // 从DatabaseTestBase获取已创建的数据库文件路径
            var dbTestBase = this as DatabaseTestBase;
            var testDbPathField = typeof(DatabaseTestBase).GetField("_testDbPath",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var testDbPath = testDbPathField?.GetValue(dbTestBase) as string;

            if (!string.IsNullOrEmpty(testDbPath))
            {
                return $"Data Source={testDbPath}";
            }

            // 如果无法获取，则创建新的临时数据库文件
            var tempDbPath = Path.Combine(Path.GetTempPath(), $"test_gcp_{Guid.NewGuid():N}.db");
            return $"Data Source={tempDbPath}";
        }

        protected override async Task SeedTestDataAsync(GcpDb db)
        {
            try
            {
                // 创建基础测试数据 - 使用唯一ID避免冲突
                var timestamp = DateTime.Now.Ticks.ToString().Substring(10); // 使用时间戳后8位
                var solutionId = $"test-sol-act-{timestamp}";
                var projectId = $"test-proj-act-{timestamp}";

                var solution = TestDataBuilder.CreateTestSolution("工作流动作测试解决方案");
                solution.Id = solutionId;
                await db.InsertAsync(solution);

                var project = TestDataBuilder.CreateTestProject("工作流动作测试项目", solutionId);
                project.Id = projectId;
                await db.InsertAsync(project);

                // 创建测试数据源
                var dataSource = TestDataBuilder.CreateTestDataSource("测试数据源", solutionId, projectId);
                dataSource.Id = "test-datasource-001"; // 使用固定ID，与测试中的DataSource匹配
                dataSource.ConnectBy = "ConnectionString"; // 设置必需字段
                await db.InsertAsync(dataSource);

                // 创建测试API
                var api = TestDataBuilder.CreateTestApi("测试API", solutionId, projectId);
                api.Id = $"test-api-act-{timestamp}";
                await db.InsertAsync(api);

                Output.WriteLine("工作流动作测试数据初始化完成");
            }
            catch (Exception ex)
            {
                Output.WriteLine($"工作流动作测试数据初始化失败: {ex.Message}");
                throw;
            }
        }
    }
}
