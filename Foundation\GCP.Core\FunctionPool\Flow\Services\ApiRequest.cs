﻿using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow.Models;
using GCP.Functions.Common.Models;
using GCP.Functions.Common.ScriptExtensions;
using GCP.Functions.Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using RestSharp;
using System.Diagnostics;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Web;
using Yarp.ReverseProxy.Forwarder;

namespace GCP.FunctionPool.Flow.Services
{
    class ApiRequest : DataBaseService
    {
        // 使用全局API客户端管理器
        private readonly ApiClientManager _clientManager = GlobalApiClientManager.Instance;

        private (LcApi api, Uri Url) GetApiUri(string apiId)
        {
            var service = new ApiService();
            var data = service.GetApiById(apiId);
            if (data == null)
            {
                throw new CustomException("API接口不存在");
            }

            Uri url;
            var baseUrlCode = data.BaseUrl;
            if (string.IsNullOrEmpty(baseUrlCode))
            {
                url = new Uri(data.HttpUrl);
            }
            else
            {
                var dicService = new DataDictionaryService(this.Context, data.SolutionId, data.ProjectId);
                var baseUrl = dicService.GetByCode("API_BASE_URL", baseUrlCode)?.DictValue;
                if (string.IsNullOrEmpty(baseUrl))
                {
                    throw new CustomException($"未配置 API_BASE_URL:${baseUrlCode} 的字典项，请配置服务地址");
                }

                url = new Uri(new Uri(baseUrl), data.HttpUrl);
            }

            return (data, url);
        }

        private List<FlowData> GetApiParamsFlowData(LcApi api)
        {
            try
            {
                if (string.IsNullOrEmpty(api.QueryParameters))
                    return new List<FlowData>();

                return JsonHelper.Deserialize<List<FlowData>>(api.QueryParameters);
            }
            catch
            {
                return new List<FlowData>();
            }
        }

        private List<FlowData> GetApiBodyFlowData(LcApi api)
        {
            try
            {
                if (string.IsNullOrEmpty(api.Body))
                    return new List<FlowData>();

                return JsonHelper.Deserialize<List<FlowData>>(api.Body);
            }
            catch
            {
                return new List<FlowData>();
            }
        }

        /// <summary>
        /// 根据API数据获取客户端
        /// </summary>
        public RestClient GetClientForApi(LcApi api, Uri url)
        {
            string baseUrl = $"{url.Scheme}://{url.Authority}";
            
            return _clientManager.GetClient(api.TimeoutInSeconds, baseUrl, new Dictionary<string, string>
            {
                { "Content-Type", "application/json; charset=utf-8" }
            });
        }

        [Function("apiRequestV2", "API 请求 V2")]
        public async Task<object> RequestApiV2(DataApiRequestV2 dataRequest)
        {
            (LcApi api, Uri url) = GetApiUri(dataRequest.ApiId);
            var method = api.RequestType;

            var client = GetClientForApi(api, url);

            var engine = this.GetEngine();

            var restMethod = method == "GET" ? Method.Get : Method.Post;
            var request = new RestRequest(url, restMethod);

            var localVariables = new Dictionary<string, object>();

            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine($"{restMethod.ToString().ToUpper()} {request.Resource} HTTP/{request.Version}");
            }

            string args = "";

            // 处理请求头
            if (dataRequest.Headers != null && dataRequest.Headers.Count > 0)
            {
                var headersDic = new Dictionary<string, object>();
                FlowUtils.BindResult(dataRequest.Headers, headersDic, engine, this.Context);

                foreach (var item in headersDic)
                {
                    if (item.Value != null)
                    {
                        request.AddHeader(item.Key, item.Value.ToString());

                        if (this.Context.Persistence)
                        {
                            this.Context.Current.ArgsBuilder.AppendLine($"{item.Key}: {item.Value}");
                        }
                    }
                }
            }

            // 处理请求参数（GET请求）
            if (restMethod == Method.Get && dataRequest.Params != null && dataRequest.Params.Count > 0)
            {
                var paramsDic = new Dictionary<string, object>();
                FlowUtils.BindResult(dataRequest.Params, paramsDic, engine, this.Context);

                foreach (var item in paramsDic)
                {
                    if (item.Value != null)
                    {
                        request.AddQueryParameter(item.Key, item.Value.ToString(), true);
                    }
                }
            }

            // 处理请求体（POST请求）
            if (restMethod == Method.Post && dataRequest.Body != null && dataRequest.Body.Count > 0)
            {
                var bodyDic = new Dictionary<string, object>();
                FlowUtils.BindResult(dataRequest.Body, bodyDic, engine, this.Context);

                var bodyJson = JsonHelper.Serialize(bodyDic);
                args = bodyJson;
                request.AddJsonBody(bodyJson);

                if (this.Context.Persistence)
                {
                    this.Context.Current.ArgsBuilder.AppendLine();
                    this.Context.Current.ArgsBuilder.AppendLine(bodyJson);
                }
            }

            // 执行请求
            var response = await client.ExecuteAsync(request);

            if (response.IsSuccessful)
            {
                if (response.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    //Log.Error("请求失败 {path} {args} {errorMessage}", data.HttpUrl, args, response.ErrorMessage);
                    await this.Context.SqlLog.Error(response.ErrorException, $"请求失败 {url} {args}");
                    throw new CustomException(response.Content);
                }

                try
                {
                    var responseData = new JavascriptUtils().JSON_PARSE(response.Content);

                    // 如果配置了响应体验证，进行验证
                    if (!string.IsNullOrEmpty(dataRequest.ResponseId))
                    {
                        var validationResult = await ValidateApiResponse(dataRequest.ResponseId, responseData);
                        if (!validationResult.IsSuccess)
                        {
                            throw new CustomException($"API响应验证失败: {validationResult.ErrorMessage}");
                        }
                        return validationResult.Data;
                    }

                    return responseData;
                }
                catch (Exception ex)
                {
                    await this.Context.SqlLog.Error(ex, $"请求返回内容转换失败 {url} {args} {response.Content}");
                    throw new CustomException(ex.Message);
                }
            }
            else
            {
                //Log.Error("请求失败 {path} {args} {errorMessage}", data.HttpUrl, args, response.ErrorMessage);
                await this.Context.SqlLog.Error(response.ErrorException, $"请求失败 {url} {args}");
                throw new CustomException(response.Content ?? response.ErrorException.Message);
            }
        }

        [Function("apiRequest", "API 请求")]
        public async Task<object> RequestApi(DataApiRequest dataRequest)
        {
            (LcApi api, Uri url) = GetApiUri(dataRequest.ApiId);
            var method = api.RequestType;

            var client = GetClientForApi(api, url);

            var engine = this.GetEngine();
            var stepInputsDic = new Dictionary<string, object>();
            if (dataRequest.Inputs != null && dataRequest.Inputs.Count > 0)
            {
                FlowUtils.BindResult(dataRequest.Inputs, stepInputsDic, engine, this.Context);
                engine.SetValue("inputs", stepInputsDic);
            }

            var restMethod = method == "GET" ? Method.Get : Method.Post;

            var request = new RestRequest(url, restMethod);

            var localVariables = new Dictionary<string, object>()
            {
                { "inputs", stepInputsDic },
            };

            string args = "";
            if (restMethod == Method.Get && dataRequest.Params != null)
            {
                var paramsObj = GetDataValue(dataRequest.Params, engine, localVariables);
                //if (this.Context.Persistence)
                //    this.Context.current.Args.Add("Params", paramsObj);

                // 使用通用的ROOT节点处理方法
                var paramsFlowData = GetApiParamsFlowData(api);
                var processedParams = RootNodeHelper.ProcessRequestParamsByRootNode(paramsObj, paramsFlowData);
                var rootNode = RootNodeHelper.GetRootNode(paramsFlowData);

                if (rootNode != null && rootNode.Type == "array")
                {
                    // ROOT节点是数组类型，将参数序列化为数组格式
                    args = JsonHelper.Serialize(processedParams);
                    // 对于数组类型的ROOT节点，我们需要特殊处理查询参数
                    // 可以将数组序列化为JSON字符串作为单个查询参数
                    request.AddQueryParameter("data", args, true);
                }
                else
                {
                    // ROOT节点是对象类型或没有ROOT节点，使用原有逻辑
                    args = JsonHelper.Serialize(processedParams);
                    JsonDocument doc = JsonDocument.Parse(args);
                    JsonElement root = doc.RootElement;
                    if (root.ValueKind == JsonValueKind.Object)
                    {
                        foreach (JsonProperty prop in root.EnumerateObject())
                        {
                            request.AddQueryParameter(prop.Name, prop.Value.ToString(), true);
                        }
                    }
                    else
                    {
                        // 如果不是对象，作为单个参数传递
                        request.AddQueryParameter("data", args, true);
                    }
                }
            }

            if (this.Context.Persistence)
            {
                this.Context.Current.ArgsBuilder = new StringBuilder();
                this.Context.Current.ArgsBuilder.AppendLine($"{restMethod.ToString().ToUpper()} {request.Resource} HTTP/{request.Version}");
            }

            if (dataRequest.Headers != null)
            {
                var headersObj = GetDataValue(dataRequest.Headers, engine, localVariables);
                var headersStr = JsonHelper.Serialize(headersObj);
                var headers = JsonHelper.Deserialize<Dictionary<string, string>>(headersStr);

                foreach (var item in headers)
                {
                    request.AddHeader(item.Key, item.Value);

                    if (this.Context.Persistence)
                    {
                        this.Context.Current.ArgsBuilder.AppendLine($"{item.Key}: {item.Value}");
                    }
                }
            }

            if (restMethod == Method.Post && dataRequest.Body != null)
            {
                var bodyObj = GetDataValue(dataRequest.Body, engine, localVariables);

                // 使用通用的ROOT节点处理方法
                var bodyFlowData = GetApiBodyFlowData(api);
                var processedBody = RootNodeHelper.ProcessRequestBodyByRootNode(bodyObj, bodyFlowData);

                args = JsonHelper.Serialize(processedBody);

                if (this.Context.Persistence)
                {
                    this.Context.Current.ArgsBuilder.AppendLine();
                    this.Context.Current.ArgsBuilder.AppendLine(args);
                }

                request.AddStringBody(args, DataFormat.Json);
            }


            var response = await client.ExecuteAsync(request, this.Context.CancellationToken);

            if (response.IsSuccessful)
            {
                if (response.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    //Log.Error("请求失败 {path} {args} {errorMessage}", data.HttpUrl, args, response.ErrorMessage);
                    await this.Context.SqlLog.Error(response.ErrorException, $"请求失败 {url} {args}");
                    throw new CustomException(response.Content);
                }

                try
                {
                    var responseData = new JavascriptUtils().JSON_PARSE(response.Content);

                    // 如果配置了响应体验证，进行验证
                    if (!string.IsNullOrEmpty(dataRequest.ResponseId))
                    {
                        var validationResult = await ValidateApiResponse(dataRequest.ResponseId, responseData);
                        if (!validationResult.IsSuccess)
                        {
                            throw new CustomException($"API响应验证失败: {validationResult.ErrorMessage}");
                        }
                        return validationResult.Data;
                    }

                    return responseData;
                }
                catch (Exception ex)
                {
                    await this.Context.SqlLog.Error(ex, $"请求返回内容转换失败 {url} {args} {response.Content}");
                    throw new CustomException(ex.Message);
                }
            }
            else
            {
                //Log.Error("请求失败 {path} {args} {errorMessage}", data.HttpUrl, args, response.ErrorMessage);
                await this.Context.SqlLog.Error(response.ErrorException, $"请求失败 {url} {args}");
                throw new CustomException(response.Content ?? response.ErrorException.Message);
            }
        }

        static SocketsHttpHandler CreateHandler()
        {
            var handler = new SocketsHttpHandler
            {
                UseProxy = false,
                AllowAutoRedirect = false,
                AutomaticDecompression = DecompressionMethods.None,
                UseCookies = false,
                EnableMultipleHttp2Connections = true,
                ActivityHeadersPropagator = new ReverseProxyPropagator(DistributedContextPropagator.Current),
                ConnectTimeout = TimeSpan.FromSeconds(15),
            };

            handler.SslOptions.RemoteCertificateValidationCallback = delegate { return true; };

            return handler;
        }

        [Function("apiForwarder", "请求转发")]
        public async Task<object> ForwarderApi(DataApiForwarder dataRequest)
        {
            HttpMessageInvoker forwarderHttpClient = new(CreateHandler());
            var engine = this.GetEngine();

            string destPath = "";
            if (dataRequest.Mode == 1)
            {
                destPath = GetApiUri(dataRequest.ApiId).Url?.ToString();
            }
            else if (dataRequest.Mode == 2)
            {
                destPath = Convert.ToString(GetDataValue(dataRequest.Url, engine));
            }

            if (string.IsNullOrEmpty(destPath))
            {
                throw new CustomException("未配置转发目标");
            }
            var ctx = this.Context.GetHttpRequest()?.HttpContext;
            if (ctx == null)
            {
                throw new CustomException("未获取到请求上下文");
            }

            var uri = new UriBuilder(destPath);
            if (dataRequest.Params?.Count > 0)
            {
                var dic = new Dictionary<string, object>();
                FlowUtils.BindResult(dataRequest.Params, dic, engine, this.Context);
                var query = HttpUtility.ParseQueryString(uri.Query);
                foreach (var item in dic)
                {
                    query[item.Key] = item.Value?.ToString();
                }
                uri.Query = query.ToString();
            }

            if (dataRequest.Headers?.Count > 0)
            {
                var dic = new Dictionary<string, object>();
                FlowUtils.BindResult(dataRequest.Headers, dic, engine, this.Context);
                foreach (var item in dic)
                {
                    ctx.Request.Headers.Append(item.Key, item.Value?.ToString());
                }
            }

            ctx.Request.Path = "";

            var proxyResponseStream = new MemoryStream();
            ctx.Response.Body = proxyResponseStream;

            var url = uri.ToString();
            var forwarder = ServiceLocator.Current.GetRequiredService<IHttpForwarder>();
            var error = await forwarder.SendAsync(ctx, url, forwarderHttpClient, (ctx, req) =>
            {
                if (this.Context.Persistence)
                {
                    this.Context.Current.ArgsBuilder = new StringBuilder();
                    this.Context.Current.ArgsBuilder.AppendLine($"{req.Method} {url} HTTP/{req.Version}");
                    this.Context.Current.ArgsBuilder.AppendLine(req.Headers.ToString());
                    this.Context.Current.ArgsBuilder.AppendLine(req.Content?.ReadAsStringAsync().Result);
                }

                return default;
            });

            if (error != ForwarderError.None)
            {
                var errorFeature = ctx.GetForwarderErrorFeature();
                var exception = errorFeature.Exception;
                if (exception != null) throw exception;
            }

            // 重置位置到流的开始
            proxyResponseStream.Position = 0;
            var proxyResponseText = StreamToString(proxyResponseStream);

            return proxyResponseText;
            //return new JavascriptUtils().JSON_PARSE(proxyResponseText);
        }

        private static string StreamToString(Stream stream)
        {
            using var reader = new StreamReader(stream, Encoding.UTF8, leaveOpen: true);
            return reader.ReadToEnd();
        }

        /// <summary>
        /// 验证API响应是否成功
        /// </summary>
        private async Task<ApiValidationResult> ValidateApiResponse(string responseId, object responseData)
        {
            await using var db = new GcpDb();
            var responseConfig = db.LcApiResponses.FirstOrDefault(r => r.Id == responseId && r.State == 1);

            if (responseConfig == null)
            {
                return new ApiValidationResult
                {
                    IsSuccess = false,
                    ErrorMessage = "未找到响应体配置"
                };
            }

            try
            {
                if(responseData is not Dictionary<string, object> responseObj) throw new ArgumentException("响应数据不是字典类型");

                // 验证成功标识
                if (!string.IsNullOrEmpty(responseConfig.SuccessFlag))
                {
                    var successConfig = JsonHelper.Deserialize<Dictionary<string, object>>(responseConfig.SuccessFlag);
                    foreach (var config in successConfig)
                    {
                        if (responseObj.TryGetValue(config.Key, out var value))
                        {
                            var actualValue = value?.ToString();
                            var expectedValue = config.Value?.ToString();
                            if (actualValue != expectedValue)
                            {
                                return new ApiValidationResult
                                {
                                    IsSuccess = false,
                                    ErrorMessage = GetErrorMessage(responseObj, responseConfig.ErrorMessage),
                                    Data = GetResultData(responseObj, responseConfig.Result)
                                };
                            }
                        }
                        else
                        {
                            return new ApiValidationResult
                            {
                                IsSuccess = false,
                                ErrorMessage = $"响应中缺少成功标识字段: {config.Key}"
                            };
                        }
                    }
                }

                return new ApiValidationResult
                {
                    IsSuccess = true,
                    Data = GetResultData(responseObj, responseConfig.Result)
                };
            }
            catch (Exception ex)
            {
                return new ApiValidationResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"验证响应时发生错误: {ex.Message}"
                };
            }
        }

        private string GetErrorMessage(Dictionary<string, object> responseObj, string errorMessageConfig)
        {
            if (string.IsNullOrEmpty(errorMessageConfig))
                return "API调用失败";

            try
            {
                var errorConfig = JsonHelper.Deserialize<Dictionary<string, string>>(errorMessageConfig);
                foreach (var config in errorConfig)
                {
                    if (responseObj.TryGetValue(config.Value, out var value))
                    {
                        return value?.ToString() ?? "未知错误";
                    }
                }
            }
            catch
            {
                // 如果配置格式错误，直接作为字段名使用
                if (responseObj.TryGetValue(errorMessageConfig, out var value))
                {
                    return value?.ToString() ?? "未知错误";
                }
            }

            return "API调用失败";
        }

        private object GetResultData(Dictionary<string, object> responseObj, string resultConfig)
        {
            if (string.IsNullOrEmpty(resultConfig))
                return responseObj;

            try
            {
                var dataConfig = JsonHelper.Deserialize<Dictionary<string, string>>(resultConfig);
                foreach (var config in dataConfig)
                {
                    if (responseObj.TryGetValue(config.Value, out var data))
                    {
                        return data;
                    }
                }
            }
            catch
            {
                // 如果配置格式错误，直接作为字段名使用
                if (responseObj.TryGetValue(resultConfig, out var data))
                {
                    return data;
                }
            }

            return responseObj;
        }
    }
}
