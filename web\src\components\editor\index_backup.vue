<template>
  <vue-monaco-editor
    class="editor"
    :language="props.language"
    theme="vs-dark"
    :options="MONACO_EDITOR_OPTIONS"
    @mount="handleMount"
  />
</template>
<script lang="ts">
export default {
  name: 'Editor',
};
</script>
<script setup lang="ts">
import { VueMonacoEditor } from '@guolao/vue-monaco-editor';
import { editor } from 'monaco-editor';
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api';
import { computed, shallowRef } from 'vue';

const props = withDefaults(
  defineProps<{
    code?: string;
    language: string;
    readOnly?: boolean;
    autoWrap?: boolean;
    showLineNumbers?: boolean;
  }>(),
  {
    language: 'javascript',
    readOnly: false,
    autoWrap: true,
    showLineNumbers: true,
  },
);

const MONACO_EDITOR_OPTIONS = computed<editor.IStandaloneEditorConstructionOptions>(() => {
  const options: editor.IStandaloneEditorConstructionOptions = {
    automaticLayout: true,
    readOnly: props.readOnly,
    formatOnType: true,
    formatOnPaste: true,
    lineNumbers: props.showLineNumbers ? 'on' : 'off',
  };
  if (props.autoWrap) {
    options.wordWrap = 'on';
    options.minimap = {
      enabled: false,
    };
  }
  return options;
});

const editorRef = shallowRef<Partial<monaco.editor.IStandaloneCodeEditor>>();
const handleMount = (editor: any) => {
  editorRef.value = editor;
};

const formatCode = () => {
  editorRef.value?.getAction('editor.action.formatDocument').run();
};

const insertText = (text) => {
  const selection = editorRef.value.getSelection();
  const range = new monaco.Range(
    selection.startLineNumber,
    selection.startColumn,
    selection.endLineNumber,
    selection.endColumn,
  );

  editorRef.value.executeEdits('insertText', [
    {
      range,
      text,
      forceMoveMarkers: true,
    },
  ]);

  editorRef.value.focus();
};
defineExpose({
  formatCode,
  insertText,
  editorRef,
});
</script>
<style lang="less" scoped>
.editor {
  border: 1px solid var(--td-border-level-2-color);
  border-radius: var(--td-radius-default);
}
</style>
