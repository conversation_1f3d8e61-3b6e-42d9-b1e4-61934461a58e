import { cloneDeep, intersectionBy } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowControl, FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import { ArgsInfo } from './model';

const control: FlowControl = {
  forEach: {
    async: false,
    item: [],
    list: '',
    nextId: '',
  },
};
const sqlParamsRegex = /[@:]((\w|[\u4e00-\u9fa5])+)/g;

export type forEachInfo = typeof control.forEach;

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();

  const currentArgs = actionFlowStore.currentStep.args || {};
  const isNewAction = !currentArgs.name; // 判断是否为新增动作

  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      dataSource: '',
      autoPaged: false,
      isPaging: false,
      hasTotal: false,
      pageSize: {
        type: 'text',
        textValue: '500',
      },
      pageIndex: {
        type: 'text',
        textValue: '1',
      },
      description: '',
      operateType: 'configure',
      sqlInfo: {
        sql: '',
        parameters: [],
      },
      configureInfo: [
        {
          id: 'root',
          tableData: {
            tableName: '',
            tableDescription: '',
            columns: [],
            sortColumns: [],
            conditions: null,
          },
        },
      ],
      outputConfig: {
        type: 'list',
        dictionaryConfig: {
          keyColumns: [],
          keySeparator: '|',
          valueColumn: '',
          useFullObjectAsValue: false,
        },
      },
      useRoot: isNewAction ? true : (currentArgs.useRoot ?? false), // 新增动作默认true，历史配置默认false
    } as ArgsInfo),
    ...currentArgs,
  }) as ArgsInfo;

  state.forEachData = cloneDeep(actionFlowStore.currentStep.control?.forEach || control.forEach) as forEachInfo;
};

export const useDataQueryStore = defineStore('DataQuery', {
  state: () => {
    const state = { args: null, forEachData: null } as { args: ArgsInfo; forEachData: forEachInfo };
    initialState(state);
    return state;
  },
  getters: {
    variables() {
      const hasTotal = this.args.hasTotal && this.args.isPaging;
      const outputType = this.args.outputConfig?.type || 'list';
      const pagedVars: FlowData[] = [];

      // 分页且有总数时，后端总是返回 {list: [], total: number} 结构，忽略 OutputConfig
      if (hasTotal) {
        const itemKey = 'result.list';
        const children =
          this.args.configureInfo[0]?.tableData?.columns?.map((column) => {
            const itemPath = `${itemKey}.${column.columnName}`;
            return {
              id: column.columnName,
              key: column.columnName,
              description: column.description,
              type: column.dataType,
              value: {
                type: 'variable',
                dataType: column.dataType,
                variableType: 'current',
                variableValue: itemPath,
              },
            } as FlowData;
          }) || [];

        pagedVars.push({
          id: 'result',
          key: 'result',
          description: '分页结果',
          type: 'object',
          children: [
            {
              id: 'total',
              key: 'total',
              description: '总数',
              type: 'int',
              value: {
                type: 'variable',
                dataType: 'int',
                variableType: 'current',
                variableValue: 'result.total',
              },
            },
            {
              id: 'list',
              key: 'list',
              description: '数据清单',
              type: 'array',
              value: {
                type: 'variable',
                dataType: 'array',
                variableType: 'current',
                variableValue: itemKey,
              },
              children,
            },
          ],
        });
      } else {
        // 非分页或分页无总数时，根据输出类型生成不同的变量结构
        if (outputType === 'count') {
          // count 类型：返回数字
          pagedVars.push({
            id: 'result',
            key: 'result',
            description: '记录数量',
            type: 'int',
            value: {
              type: 'variable',
              dataType: 'int',
              variableType: 'current',
              variableValue: 'result',
            },
          });
        } else if (outputType === 'single') {
          // single 类型：返回单个对象
          const children =
            this.args.configureInfo[0]?.tableData?.columns?.map((column) => {
              return {
                id: column.columnName,
                key: column.columnName,
                description: column.description,
                type: column.dataType,
                value: {
                  type: 'variable',
                  dataType: column.dataType,
                  variableType: 'current',
                  variableValue: `result.${column.columnName}`,
                },
              } as FlowData;
            }) || [];

          pagedVars.push({
            id: 'result',
            key: 'result',
            description: '单条记录',
            type: 'object',
            children,
            value: {
              type: 'variable',
              dataType: 'object',
              variableType: 'current',
              variableValue: 'result',
            },
          });
        } else if (outputType === 'dictionary') {
          // dictionary 类型：返回字典对象
          pagedVars.push({
            id: 'result',
            key: 'result',
            description: '字典结果',
            type: 'object',
            value: {
              type: 'variable',
              dataType: 'object',
              variableType: 'current',
              variableValue: 'result',
            },
          });
        } else {
          // list 类型：返回数组（默认行为）
          const children =
            this.args.configureInfo[0]?.tableData?.columns?.map((column) => {
              const itemPath = `result.${column.columnName}`;
              return {
                id: column.columnName,
                key: column.columnName,
                description: column.description,
                type: column.dataType,
                value: {
                  type: 'variable',
                  dataType: column.dataType,
                  variableType: 'current',
                  variableValue: itemPath,
                },
              } as FlowData;
            }) || [];

          pagedVars.push({
            id: 'list',
            key: 'list',
            description: '查询结果',
            type: 'array',
            children,
            value: {
              type: 'variable',
              dataType: 'array',
              variableType: 'current',
              variableValue: 'result',
            },
          });
        }
      }

      return [...pagedVars] as FlowData[];
    },
  },
  actions: {
    updateState() {
      initialState(this);
    },
    setArgs(args: ArgsInfo) {
      const actionFlowStore = useActionFlowStore();
      actionFlowStore.setCurrentName(args.name);
      actionFlowStore.setCurrentArgs(args);

      // 先更新 this.args，然后再计算 variables
      this.args = args;

      // 如果不使用根节点，直接设置原始变量结构
      if (!args.useRoot) {
        actionFlowStore.setStepResultData(this.variables);
        return;
      }

      // 使用根节点时的处理逻辑
      const resultVariable = this.variables.find((item) => item.id === 'result');
      if (resultVariable) {
        // 分页且有总数时，使用固定的分页结构
        if (args.hasTotal && args.isPaging) {
          const resultNode = {
            id: 'result',
            key: 'result',
            description: '分页结果',
            type: 'object',
            value: {
              type: 'variable' as const,
              variableType: 'current' as const,
              variableValue: 'result',
              dataType: 'object',
            },
            children: resultVariable.children || [],
          };
          actionFlowStore.setStepResultData([resultNode]);
          return;
        }

        // 非分页或分页无总数：根据输出类型设置根节点
        const outputType = args.outputConfig?.type || 'list';
        let outputKey = 'result';
        let description = '查询结果';

        // 根据输出类型设置描述
        switch (outputType) {
          case 'count':
            description = '记录数量';
            break;
          case 'single':
            description = '单条记录';
            break;
          case 'dictionary':
            description = '字典结果';
            break;
          case 'list':
          default:
            outputKey = 'list';
            description = '查询结果';
            break;
        }

        const shouldIncludeChildren = resultVariable.children && outputType !== 'count' && outputType !== 'dictionary';

        const resultNode = {
          id: outputKey,
          key: outputKey,
          description,
          type: resultVariable.type,
          value: {
            type: 'variable' as const,
            variableType: 'current' as const,
            variableValue: 'result',
            dataType: resultVariable.type,
          },
          // 只有当 resultVariable 有 children 且不是 count/dictionary 类型时才包含 children
          ...(shouldIncludeChildren ? { children: resultVariable.children } : {}),
        };

        actionFlowStore.setStepResultData([resultNode]);
      }
    },
    setSqlParameters(sql: string) {
      const actionFlowStore = useActionFlowStore();

      const matches = [];
      let match;
      do {
        match = sqlParamsRegex.exec(sql);
        if (match && match.length > 1) {
          matches.push({
            paramName: match[1],
            paramCode: match[0],
            paramValue: '',
          });
        }
      } while (match);

      if (this.args.sqlInfo.parameters && this.args.sqlInfo.parameters.length > 0) {
        const intersection = intersectionBy(this.args.sqlInfo.parameters, matches, 'paramName') as any;
        for (const param of intersection) {
          const index = matches.findIndex((item) => item.paramName === param.paramName);
          matches[index] = param;
        }
      }
      this.args.sqlInfo.parameters = matches;
      actionFlowStore.setCurrentArgs(this.args);
    },
  },
});
