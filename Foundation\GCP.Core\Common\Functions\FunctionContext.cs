﻿using GCP.DataAccess;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http;
using System.Text.Json.Serialization;

namespace GCP.Common
{
    public delegate Task FunctionDelegate(FunctionContext context);
    public delegate Task FunctionMiddlewareDelegate(FunctionContext context, Func<Task> next);

    public partial class FunctionContext : IDisposable
    {
        /// <summary>
        /// 数据库实例
        /// </summary>
        [JsonIgnore]
        public AsyncLocal<IDbContext> LocalDbContext { get; set; } = new AsyncLocal<IDbContext>();
        [JsonIgnore]
        public DbContext db { get { return (DbContext)LocalDbContext.Value; } }
        [JsonIgnore]
        internal IServiceScope scope { get; set; }

        public string clientID { get; set; }

        private Logger _log;
        [JsonIgnore]
        public Logger Log
        {
            get
            {
                if (_log == null)
                {
                    _log = LoggerManager.GetLogger(clientID);
                }
                return _log;
            }
        }

        private SqlContextLogger _sqlLog;
        [JsonIgnore]
        public SqlContextLogger SqlLog
        {
            get
            {
                if (_sqlLog == null)
                {
                    _sqlLog = this.GetFuncSqlLogger();
                }
                return _sqlLog;
            }
        }

        internal string SolutionId { get; set; }
        internal string ProjectId { get; set; }
        public string Path { get; set; }

        public string trackId { get; set; }
        public bool ThrowError { get; set; } = true;
        /// <summary>
        /// 是否持久化, 支持断点执行（默认否, 开启会影响性能）
        /// </summary>
        public bool Persistence { get; set; } = false;

        [JsonIgnore]
        public CancellationToken CancellationToken { get; set; } = CancellationToken.None;

        /// <summary>
        /// 环境变量
        /// </summary>
        public Dictionary<string, object> globalData
        {
            get => LocalGlobalArgs;
            set => LocalGlobalArgs = value;
        }
        private Dictionary<string, object> LocalGlobalArgs { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 函数入参
        /// </summary>
        public IDictionary<string, object> Args { get; set; }

        /// <summary>
        /// 执行结果
        /// </summary>
        public object Result { get; set; }

        /// <summary>
        /// 当前执行函数相关信息
        /// </summary>
        private AsyncLocal<FunctionProvider> _currentProvider = new AsyncLocal<FunctionProvider>();
        public FunctionProvider Current 
        { 
            get => _currentProvider.Value; 
            set => _currentProvider.Value = value; 
        }

        [JsonIgnore]
        public FunctionProvider LastData { get; set; }

        /// <summary>
        /// 全局中间件, 每个函数都会经过该管道
        /// </summary>
        [JsonIgnore]
        public List<FunctionMiddlewareDelegate> Middlewares
        {
            get => LocalMiddlewares.Value;
            set => LocalMiddlewares.Value = value;
        }
        AsyncLocal<List<FunctionMiddlewareDelegate>> LocalMiddlewares { get; set; } = new AsyncLocal<List<FunctionMiddlewareDelegate>>();

        /// <summary>
        /// 顺序
        /// </summary>
        internal int SeqNo { get; set; }
        [JsonIgnore]
        internal CancellationTokenSource BreakCancellationTokenSource { get; set; }
        [JsonIgnore]
        internal CancellationTokenSource ContinueCancellationTokenSource { get; set; }
        /// <summary>
        /// 是否开启事务
        /// </summary>
        public AsyncLocal<bool> GlobalTransaction { get; set; } = new AsyncLocal<bool>();

        public FunctionContext()
        {
            scope = ServiceLocator.Current.CreateScope();
            globalData = new Dictionary<string, object>();
            Args = new Dictionary<string, object>();
            Middlewares = new List<FunctionMiddlewareDelegate>();
        }

        public HttpRequest GetHttpRequest()
        {
            return globalData != null && globalData.TryGetValue("$_Request", out var request)
                ? (HttpRequest)request
                : null;
        }

        internal void SetHttpRequest(HttpRequest request)
        {
            SolutionId = request.Headers["sid"];
            ProjectId = request.Headers["pid"];
            globalData["$_Request"] = request;
        }

        public void Dispose()
        {
            LocalDbContext = null;
            scope.Dispose();
        }
    }
}
