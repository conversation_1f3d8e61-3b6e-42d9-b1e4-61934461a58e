import { customAlphabet } from 'nanoid';

import { api, Services } from '@/api/system';

import { FlowData, FlowInfo, FlowStep } from '../model';

const nanoid = customAlphabet('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 13);

export const getRandomId = () => {
  return nanoid();
};

export const getActionList = (startId: string, allData: FlowStep[]): FlowStep[] => {
  const list: FlowStep[] = [];
  setActionList(startId, list, allData);
  return list;
};

const setActionList = (nextId: string, list: FlowStep[], allData: FlowStep[]) => {
  if (!nextId) return;
  const item = allData?.find((item) => item.id === nextId);
  if (item) {
    list.push(item);
    setActionList(item.nextId, list, allData);
  }
};

export const getSubNextId = (item: FlowStep): string => {
  if (item.controlType) {
    switch (item.controlType) {
      case 'branch':
        return item.control?.branch?.hasData.nextId;
      case 'forEach':
        return item.control?.forEach.nextId;
      case 'while':
        return item.control?.while.nextId;
      case 'transaction':
        return item.control?.transaction.nextId;
      case 'tryCatch':
        return item.control?.tryCatch.nextId;
      default:
        return null;
    }
  }
  return null;
};

export const getAllSubIds = (item: FlowStep, allData: FlowStep[]): string[] => {
  const list: string[] = [];
  getAllSubIdsByRecursion(item, list, allData);
  return list;
};

const getAllSubIdsByRecursion = (item: FlowStep, list: string[], allData: FlowStep[]) => {
  list.push(item.id);
  const nextId = item.controlType ? getSubNextId(item) : item.nextId;
  const nextItem = allData.find((t) => t.id === nextId);
  if (!nextItem) {
    return;
  }

  getAllSubIdsByRecursion(nextItem, list, allData);
};

export const getVariableList = (item: FlowStep, flowInfo: FlowInfo): FlowData[] => {
  if (!flowInfo) return [];
  const globalData = (flowInfo.data || []).filter((t) => t.key);
  const result = [...globalData, ...getPreData(item, flowInfo.body)];
  recursionVariableList(result || []);
  return result;
};

const recursionVariableList = (list: FlowData[], parent?: FlowData) => {
  for (const item of list) {
    item.path = parent ? `${parent.path}.${item.key}` : item.key;
    item.pathDescription = parent
      ? `${parent.pathDescription || parent.key}.${item.description || item.key}`
      : item.description || item.key;
    if (item.children && item.children.length > 0) {
      recursionVariableList(item.children, item);
    }
  }
};

export const getPreData = (item: FlowStep, allData: FlowStep[]): FlowData[] => {
  const data: FlowData[] = [];
  let preItem: FlowStep = item;
  do {
    const preItemData = getPreItem(preItem, allData);
    preItem = preItemData?.item;
    if (preItem) {
      const children = [...(preItem.result || [])];
      if (
        preItemData.isNest &&
        preItem.controlType === 'forEach' &&
        Array.isArray(preItem.control?.forEach?.item) &&
        preItem.control?.forEach?.item.length > 0
      ) {
        for (const item of preItem.control.forEach.item) {
          children.push(item);
        }
      }

      if (children.length > 0)
        data.push({
          id: preItem.id,
          key: preItem.id,
          type: 'object',
          description: preItem.name,
          children,
        });
    }
  } while (preItem);

  return data;
};

export const getPreItem = (item: FlowStep, allData: FlowStep[]) => {
  if (!item) return null;
  for (let i = 0; i < allData.length; i++) {
    const currentItem = allData[i];
    if (currentItem.nextId === item.id) {
      return {
        item: currentItem,
        isNest: false,
      };
    }
    if (currentItem.controlType) {
      const subNextId = getSubNextId(currentItem);
      if (subNextId === item.id) {
        return {
          item: currentItem,
          isNest: true,
        };
      }
    }
  }
  return null;
};

export const getPreId = (item: FlowStep, allData: FlowStep[]): string => {
  return getPreItem(item, allData)?.item?.id;
};

/**
 * 删除节点和其子节点
 * @param id
 * @param allData
 * @returns
 */
export const removeAction = (id: string, allData: FlowStep[]) => {
  const item = allData.find((t) => t.id === id);
  if (!item) return allData;

  const preItem = getPreItem(item, allData);
  if (preItem) {
    setNextIdByParent(preItem.item, item.nextId || '', preItem.isNest);
  } else if (item.nextId) {
    const deleteIndex = allData.findIndex((t) => t.id === item.nextId);
    const nextItem = allData[deleteIndex];
    allData.splice(deleteIndex, 1);
    allData.splice(0, 0, nextItem);
  }

  if (item.controlType) {
    const subIds = getAllSubIds(item, allData);
    return allData.filter((t) => !subIds.includes(t.id));
  }

  return allData.filter((t) => t.id !== id);
};

export const moveAction = (
  source: FlowStep,
  target: FlowStep,
  position: 'before' | 'inside' | 'after',
  allData: FlowStep[],
) => {
  if (!source || !target) return;

  // 如果是移动到自己，直接返回
  if (source.id === target.id) return;

  // 检查是否是移动到自己的子节点中
  if (position === 'inside' && source.controlType) {
    const subIds = getAllSubIds(source, allData);
    if (subIds.includes(target.id)) {
      return;
    }
  }

  const sourcePreItem = getPreItem(source, allData);
  if (sourcePreItem) {
    // 如果source有前置项，将前置项的nextId指向source的nextId
    setNextIdByParent(sourcePreItem.item, source.nextId, sourcePreItem.isNest);
  } else {
    // 如果source是第一个元素，需要特殊处理
    // 找到source的下一个元素，将其设为新的第一个元素
    if (source.nextId) {
      const nextItem = allData.find((t) => t.id === source.nextId);
      if (nextItem) {
        const sourceIndex = allData.findIndex((t) => t.id === source.id);
        const nextIndex = allData.findIndex((t) => t.id === source.nextId);
        if (sourceIndex === 0 && nextIndex > 0) {
          // 将下一个元素移到第一位
          allData.splice(nextIndex, 1);
          allData.splice(sourceIndex, 1);
          allData.splice(0, 0, nextItem);
          allData.push(source);
        }
      }
    } else {
      // source是唯一的元素，直接移除并添加到末尾
      const sourceIndex = allData.findIndex((t) => t.id === source.id);
      if (sourceIndex === 0) {
        allData.splice(0, 1);
        allData.push(source);
      }
    }
  }

  const targetPreItem = getPreItem(target, allData);
  if (position === 'before') {
    if (targetPreItem) {
      setNextIdByParent(targetPreItem.item, source.id, targetPreItem.isNest);
      setNextIdByParent(source, target.id, false);
    } else {
      // target是第一个元素，将source插入到最前面
      const deleteIndex = allData.findIndex((t) => t.id === source.id);
      if (deleteIndex >= 0) {
        allData.splice(deleteIndex, 1);
      }
      allData.splice(0, 0, source);
      // 修复bug：检查数组长度，避免访问不存在的元素
      if (allData.length > 1) {
        setNextIdByParent(source, allData[1].id, false);
      } else {
        setNextIdByParent(source, '', false);
      }
    }
  } else if (position === 'inside') {
    if (target.controlType) {
      const subNextId = getSubNextId(target);
      setNextIdByParent(source, subNextId || '', false);
      setNextIdByParent(target, source.id, true);
    }
  } else if (position === 'after') {
    if (target.nextId === source.id) return;
    const targetNextItem = allData.find((t) => t.id === target.nextId);
    setNextIdByParent(source, targetNextItem ? targetNextItem.id : '', false);
    setNextIdByParent(target, source.id, false);
  }
};

export const setNextId = (parentNextId: string, currentId: string, allData: FlowStep[], isNest: boolean) => {
  const item = allData.find((item) => item.id === parentNextId);
  return setNextIdByParent(item, currentId, isNest);
};

export const setNextIdByParent = (item: FlowStep, currentId: string, isNest: boolean) => {
  if (!item) return;
  if (item.controlType && isNest) {
    if (!item?.control) {
      item.control = {};
    }
    switch (item.controlType) {
      case 'branch':
        if (!item.control.branch) {
          item.control.branch = {
            hasData: {
              condition: '',
              nextId: '',
            },
            noData: {
              condition: '',
              nextId: '',
            },
          };
        }
        item.control.branch.hasData.nextId = currentId;
        break;
      case 'forEach':
        if (!item.control.forEach) {
          item.control.forEach = {
            async: false,
            item: null,
            list: '',
            nextId: '',
          };
        }
        item.control.forEach.nextId = currentId;
        break;
      case 'while':
        if (!item.control.while) {
          item.control.while = {
            condition: '',
            loopType: 'count',
            nextId: '',
          };
        }
        item.control.while.nextId = currentId;
        break;
      case 'transaction':
        if (!item.control.transaction) {
          item.control.transaction = {
            rollbackType: 'DB',
            undoToPerStep: false,
            nextId: '',
          };
        }
        item.control.transaction.nextId = currentId;
        break;
      case 'tryCatch':
        if (!item.control.tryCatch) {
          item.control.tryCatch = {
            allowRetry: false,
            retryCount: 3,
            retryDelaysInSeconds: 10,
            nextId: '',
          };
        }
        item.control.tryCatch.nextId = currentId;
        break;
      default:
        break;
    }
  } else {
    item.nextId = currentId;
  }
};

export const recursionMergeVariable = (oldData: FlowData[], newData: FlowData[]) => {
  const oldIsArray = oldData && Array.isArray(oldData);
  const newIsArray = newData && Array.isArray(newData);
  if (!(oldIsArray && newIsArray)) {
    return;
  }

  for (const newItem of newData) {
    let exist = false;
    for (let i = 0; i < oldData.length; i++) {
      const item = oldData[i];
      if (newItem.key === item.key) {
        item.path = newItem.path;
        item.pathDescription = newItem.pathDescription;

        // 对于输出参数（result节点），总是更新类型和值以确保输出配置变更生效
        if (!item.isCustomize && !newItem.isCustomize) {
          item.value = newItem.value;
          item.type = newItem.type;

          // 处理children的更新
          if (!newItem.children || newItem.children.length === 0) {
            // 当新项目没有children时，清空旧项目的children
            item.children = undefined;
          } else {
            // 当新项目有children时，确保旧项目也有children数组
            if (!item.children) {
              item.children = [];
            }
          }
        }

        // 处理children的递归合并
        if (newItem.children && newItem.children.length > 0) {
          if (!item.children) {
            item.children = [];
          }
          recursionMergeVariable(item.children, newItem.children);
        }
        exist = true;
        break;
      }
    }

    if (!exist) {
      oldData.push(newItem);
    }
  }

  const oData = oldData.filter((t) => !t.isCustomize);
  for (let i = oldData.length - 1; i >= 0; i--) {
    const item = oData.find((t) => t.key === oldData[i].key);
    if (!item) continue;
    const exist = newData.some((t) => t.key === item.key);
    if (!exist) {
      oldData.splice(i, 1);
    }
  }
};

export const setNewData = (oldAllData: FlowData[], newData: FlowData) => {
  if (!oldAllData || !newData) return;
  for (let i = 0; i < oldAllData.length; i++) {
    const item = oldAllData[i];
    if (item.id === newData.id) {
      oldAllData[i] = newData;
      return;
    }
    setNewData(item.children, newData);
  }
};

export const getGlobalVariables = async () => {
  const data = await api.run(Services.dirTreeGetTree, {
    dirType: 'D',
  });
  // 递归遍历数据，将所有变量转换为FlowData嵌套类型
  const result: FlowData[] = [];

  const transformToFlowData = (items) => {
    if (!items || !items.length) return [];

    return items.map((item) => {
      const currentPath = `_global.${item.value}`;

      // 如果是目录
      if (!item.isLeaf) {
        const children = transformToFlowData(item.children);
        return {
          id: item.id,
          key: item.value,
          description: item.label,
          type: '分组',
          children,
        } as FlowData;
      }
      // 如果是变量

      return {
        id: item.id,
        key: item.value,
        description: item.label,
        type: '变量',
        value: {
          type: 'variable',
          variableValue: currentPath,
          variableName: item.label,
          variableType: 'global',
        },
        path: currentPath,
      } as FlowData;
    });
  };

  if (data) {
    result.push(...transformToFlowData(data));
  }

  return result;
};
