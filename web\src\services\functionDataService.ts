import { api, Services } from '@/api/system';
import { updateIntelliSenseData } from '@/components/editor/scriptCompletion';

// 函数数据接口定义
export interface FunctionItem {
  value: string;
  label: string;
  script: string;
  remark: string;
  category?: string;
  categoryDisplayName?: string;
  returnType?: string;
}

export interface FunctionCategory {
  value: string;
  label: string;
  children: FunctionItem[];
}

// 函数数据管理服务
class FunctionDataService {
  private static instance: FunctionDataService;
  private functionCategories: FunctionCategory[] = [];
  private allFunctions: FunctionItem[] = [];
  private isLoading = false;
  private loadPromise: Promise<FunctionCategory[]> | null = null;

  private constructor() {}

  public static getInstance(): FunctionDataService {
    if (!FunctionDataService.instance) {
      FunctionDataService.instance = new FunctionDataService();
    }
    return FunctionDataService.instance;
  }

  /**
   * 获取按分类分组的函数数据
   */
  public async getFunctionCategories(forceRefresh = false): Promise<FunctionCategory[]> {
    if (!forceRefresh && this.functionCategories.length > 0) {
      return this.functionCategories;
    }

    if (this.isLoading && this.loadPromise) {
      return this.loadPromise;
    }

    this.isLoading = true;
    this.loadPromise = this.loadFunctionData();

    try {
      const result = await this.loadPromise;
      return result;
    } finally {
      this.isLoading = false;
      this.loadPromise = null;
    }
  }

  /**
   * 获取扁平化的所有函数列表
   */
  public async getAllFunctions(forceRefresh = false): Promise<FunctionItem[]> {
    if (!forceRefresh && this.allFunctions.length > 0) {
      return this.allFunctions;
    }

    await this.getFunctionCategories(forceRefresh);
    return this.allFunctions;
  }

  /**
   * 从后端加载函数数据
   */
  private async loadFunctionData(): Promise<FunctionCategory[]> {
    try {
      console.log('开始从后端加载函数数据...');
      const data = await api.run(Services.utilsFunctionGetByCategory);
      
      if (!data || !Array.isArray(data)) {
        console.warn('后端返回的函数数据格式不正确:', data);
        return [];
      }

      this.functionCategories = data as FunctionCategory[];
      
      // 提取所有函数到扁平化列表
      this.allFunctions = this.extractAllFunctions(this.functionCategories);
      
      // 更新智能提示数据
      this.updateIntelliSense();
      
      console.log(`成功加载 ${this.functionCategories.length} 个函数分类，共 ${this.allFunctions.length} 个函数`);
      
      return this.functionCategories;
    } catch (error) {
      console.error('加载函数数据失败:', error);
      throw new Error(`加载函数数据失败: ${error.message || error}`);
    }
  }

  /**
   * 从分类数据中提取所有函数
   */
  private extractAllFunctions(categories: FunctionCategory[]): FunctionItem[] {
    const functions: FunctionItem[] = [];
    
    categories.forEach(category => {
      if (category.children && Array.isArray(category.children)) {
        category.children.forEach(func => {
          functions.push({
            ...func,
            category: category.value,
            categoryDisplayName: category.label
          });
        });
      }
    });
    
    return functions;
  }

  /**
   * 更新智能提示数据
   */
  private updateIntelliSense(): void {
    try {
      // 转换为智能提示需要的格式
      const intelliSenseFunctions = this.allFunctions.map(func => ({
        value: func.value,
        label: func.label,
        script: func.script,
        remark: func.remark
      }));

      // 更新智能提示
      updateIntelliSenseData({ functions: intelliSenseFunctions });
      
      console.log(`已更新智能提示，包含 ${intelliSenseFunctions.length} 个函数`);
    } catch (error) {
      console.error('更新智能提示失败:', error);
    }
  }

  /**
   * 根据关键词搜索函数
   */
  public async searchFunctions(keyword: string): Promise<FunctionItem[]> {
    const allFunctions = await this.getAllFunctions();
    
    if (!keyword) {
      return allFunctions;
    }

    const lowerKeyword = keyword.toLowerCase();
    return allFunctions.filter(func => 
      func.label.toLowerCase().includes(lowerKeyword) ||
      func.value.toLowerCase().includes(lowerKeyword) ||
      func.remark.toLowerCase().includes(lowerKeyword)
    );
  }

  /**
   * 根据分类获取函数
   */
  public async getFunctionsByCategory(categoryValue: string): Promise<FunctionItem[]> {
    const categories = await this.getFunctionCategories();
    const category = categories.find(cat => cat.value === categoryValue);
    return category?.children || [];
  }

  /**
   * 清除缓存，强制重新加载
   */
  public clearCache(): void {
    this.functionCategories = [];
    this.allFunctions = [];
    console.log('函数数据缓存已清除');
  }

  /**
   * 获取加载状态
   */
  public getLoadingState(): boolean {
    return this.isLoading;
  }
}

// 导出单例实例
export const functionDataService = FunctionDataService.getInstance();

// 导出便捷方法
export const getFunctionCategories = (forceRefresh = false) => 
  functionDataService.getFunctionCategories(forceRefresh);

export const getAllFunctions = (forceRefresh = false) => 
  functionDataService.getAllFunctions(forceRefresh);

export const searchFunctions = (keyword: string) => 
  functionDataService.searchFunctions(keyword);

export const getFunctionsByCategory = (categoryValue: string) => 
  functionDataService.getFunctionsByCategory(categoryValue);

export const clearFunctionCache = () => 
  functionDataService.clearCache();
